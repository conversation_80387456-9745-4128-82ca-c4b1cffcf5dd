/**
 * Main JavaScript functionality for TradingView Clone
 */

// Global variables
let searchTimeout = null;
let currentSymbol = null;

// Document ready
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize header search
    initHeaderSearch();
    
    // Initialize market status updates
    updateMarketStatus();
    
    // Initialize trending symbols
    loadTrendingSymbols();
    
    // Initialize tooltips
    initTooltips();
    
    // Initialize AJAX forms
    initAjaxForms();
}

// Header Search Functionality
function initHeaderSearch() {
    const searchInput = document.getElementById('header-search');
    const searchResults = document.getElementById('search-results');
    const searchForm = document.getElementById('header-search-form');
    
    if (!searchInput) return;
    
    searchInput.addEventListener('input', function(e) {
        const query = e.target.value.trim();
        
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }
        
        if (query.length < 2) {
            hideSearchResults();
            return;
        }
        
        searchTimeout = setTimeout(() => {
            performSearch(query);
        }, 300);
    });
    
    searchInput.addEventListener('focus', function() {
        if (this.value.length >= 2) {
            showSearchResults();
        }
    });
    
    document.addEventListener('click', function(e) {
        if (!searchForm.contains(e.target)) {
            hideSearchResults();
        }
    });
    
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const query = searchInput.value.trim();
        if (query) {
            window.location.href = `/market?search=${encodeURIComponent(query)}`;
        }
    });
}

async function performSearch(query) {
    try {
        const response = await fetch('/market/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {
                    query: query,
                    limit: 8
                }
            })
        });
        
        const result = await response.json();
        
        if (result.result && result.result.symbols) {
            displaySearchResults(result.result.symbols);
        }
    } catch (error) {
        console.error('Search error:', error);
    }
}

function displaySearchResults(symbols) {
    const searchResults = document.getElementById('search-results');
    
    if (symbols.length === 0) {
        searchResults.innerHTML = '<div class="dropdown-item text-muted">No symbols found</div>';
    } else {
        let html = '';
        symbols.forEach(symbol => {
            const changeClass = symbol.daily_change_percent >= 0 ? 'text-success' : 'text-danger';
            html += `
                <a class="dropdown-item d-flex justify-content-between align-items-center" href="${symbol.url}">
                    <div>
                        <strong>${symbol.symbol}</strong>
                        <small class="text-muted d-block">${symbol.name}</small>
                    </div>
                    <div class="text-right">
                        <div>${symbol.current_price ? symbol.current_price.toFixed(2) : '--'}</div>
                        <small class="${changeClass}">${symbol.daily_change_percent ? symbol.daily_change_percent.toFixed(2) + '%' : '--'}</small>
                    </div>
                </a>
            `;
        });
        searchResults.innerHTML = html;
    }
    
    showSearchResults();
}

function showSearchResults() {
    const searchResults = document.getElementById('search-results');
    if (searchResults) {
        searchResults.style.display = 'block';
    }
}

function hideSearchResults() {
    const searchResults = document.getElementById('search-results');
    if (searchResults) {
        searchResults.style.display = 'none';
    }
}

// Market Status Updates
async function updateMarketStatus() {
    try {
        const response = await fetch('/market/api/market-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {}
            })
        });
        
        const result = await response.json();
        
        if (result.result) {
            displayMarketStatus(result.result.market_status);
            updateHomepageStats(result.result.statistics);
        }
    } catch (error) {
        console.error('Error updating market status:', error);
    }
}

function displayMarketStatus(marketStatus) {
    const statusElements = document.querySelectorAll('#market-status-text');
    const indicatorElements = document.querySelectorAll('#market-status-indicator');
    
    statusElements.forEach(element => {
        element.textContent = marketStatus.status;
    });
    
    indicatorElements.forEach(element => {
        element.className = `status-indicator mr-2 ${marketStatus.is_open ? 'status-open' : 'status-closed'}`;
    });
}

function updateHomepageStats(statistics) {
    const totalSymbolsElement = document.getElementById('total-symbols');
    const marketStatusElement = document.getElementById('market-status');
    const topGainerElement = document.getElementById('top-gainer');
    const topVolumeElement = document.getElementById('top-volume');
    
    if (totalSymbolsElement) {
        totalSymbolsElement.textContent = statistics.total_symbols || '--';
    }
    
    if (marketStatusElement) {
        marketStatusElement.textContent = statistics.market_status || '--';
    }
    
    if (topGainerElement && statistics.top_gainers && statistics.top_gainers.length > 0) {
        const topGainer = statistics.top_gainers[0];
        topGainerElement.textContent = `${topGainer.symbol} +${topGainer.daily_change_percent.toFixed(2)}%`;
    }
    
    if (topVolumeElement && statistics.top_gainers && statistics.top_gainers.length > 0) {
        const highVolume = statistics.top_gainers[0]; // Simplified for demo
        topVolumeElement.textContent = highVolume.symbol;
    }
}

// Trending Symbols
async function loadTrendingSymbols() {
    const trendingContainer = document.getElementById('trending-symbols-list');
    if (!trendingContainer) return;
    
    try {
        const response = await fetch('/market/api/trending', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {
                    limit: 5
                }
            })
        });
        
        const result = await response.json();
        
        if (result.result && result.result.trending_symbols) {
            displayTrendingSymbols(result.result.trending_symbols);
        }
    } catch (error) {
        console.error('Error loading trending symbols:', error);
        trendingContainer.innerHTML = '<div class="text-center text-muted p-3">Unable to load trending symbols</div>';
    }
}

function displayTrendingSymbols(symbols) {
    const trendingContainer = document.getElementById('trending-symbols-list');
    
    let html = '';
    symbols.forEach(symbol => {
        const changeClass = symbol.daily_change_percent >= 0 ? 'text-success' : 'text-danger';
        html += `
            <a href="/market/${symbol.slug}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                <div>
                    <strong>${symbol.symbol}</strong>
                    <small class="text-muted d-block">${symbol.name}</small>
                </div>
                <div class="text-right">
                    <div>${symbol.current_price ? symbol.current_price.toFixed(2) : '--'}</div>
                    <small class="${changeClass}">${symbol.daily_change_percent ? symbol.daily_change_percent.toFixed(2) + '%' : '--'}</small>
                </div>
            </a>
        `;
    });
    
    trendingContainer.innerHTML = html;
}

// Watchlist Functionality
function initWatchlistButtons(symbolSlug, inWatchlist) {
    const addBtn = document.getElementById('add-to-watchlist-btn');
    const removeBtn = document.getElementById('remove-from-watchlist-btn');
    
    if (addBtn) {
        addBtn.addEventListener('click', () => addToWatchlist(symbolSlug));
    }
    
    if (removeBtn) {
        removeBtn.addEventListener('click', () => removeFromWatchlist(symbolSlug));
    }
}

async function addToWatchlist(symbolSlug, category = 'stocks') {
    try {
        const response = await fetch(`/market/${symbolSlug}/add-to-watchlist`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {
                    category: category
                }
            })
        });
        
        const result = await response.json();
        
        if (result.result && result.result.success) {
            showSuccessMessage(result.result.message);
            toggleWatchlistButtons(true);
        } else {
            showErrorMessage(result.result.error || 'Failed to add to watchlist');
        }
    } catch (error) {
        console.error('Error adding to watchlist:', error);
        showErrorMessage('Failed to add to watchlist');
    }
}

async function removeFromWatchlist(symbolSlug) {
    try {
        const response = await fetch(`/market/${symbolSlug}/remove-from-watchlist`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {}
            })
        });
        
        const result = await response.json();
        
        if (result.result && result.result.success) {
            showSuccessMessage(result.result.message);
            toggleWatchlistButtons(false);
        } else {
            showErrorMessage(result.result.error || 'Failed to remove from watchlist');
        }
    } catch (error) {
        console.error('Error removing from watchlist:', error);
        showErrorMessage('Failed to remove from watchlist');
    }
}

function toggleWatchlistButtons(inWatchlist) {
    const addBtn = document.getElementById('add-to-watchlist-btn');
    const removeBtn = document.getElementById('remove-from-watchlist-btn');
    
    if (addBtn && removeBtn) {
        if (inWatchlist) {
            addBtn.style.display = 'none';
            removeBtn.style.display = 'block';
        } else {
            addBtn.style.display = 'block';
            removeBtn.style.display = 'none';
        }
    }
}

// Tab Content Loading
function initTabContentLoading(symbolSlug) {
    const tabs = document.querySelectorAll('#symbol-tabs a[data-toggle="tab"]');
    
    tabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            const targetId = e.target.getAttribute('href').substring(1);
            loadTabContent(targetId, symbolSlug);
        });
    });
}

async function loadTabContent(tabId, symbolSlug) {
    const contentContainer = document.getElementById(`${tabId}-content`);
    if (!contentContainer) return;
    
    // Skip if already loaded
    if (contentContainer.dataset.loaded === 'true') return;
    
    try {
        let endpoint = '';
        switch (tabId) {
            case 'news':
                endpoint = `/market/api/news/${symbolSlug}`;
                break;
            case 'technicals':
                endpoint = `/market/api/technical/${symbolSlug}`;
                break;
            case 'events':
                endpoint = `/market/api/events/${symbolSlug}`;
                break;
            default:
                return;
        }
        
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {}
            })
        });
        
        const result = await response.json();
        
        if (result.result) {
            displayTabContent(tabId, result.result);
            contentContainer.dataset.loaded = 'true';
        }
    } catch (error) {
        console.error(`Error loading ${tabId} content:`, error);
        contentContainer.innerHTML = '<div class="alert alert-danger">Failed to load content</div>';
    }
}

function displayTabContent(tabId, data) {
    const contentContainer = document.getElementById(`${tabId}-content`);
    
    switch (tabId) {
        case 'news':
            displayNewsContent(contentContainer, data.news || []);
            break;
        case 'technicals':
            displayTechnicalContent(contentContainer, data.indicators || {});
            break;
        case 'events':
            displayEventsContent(contentContainer, data.events || []);
            break;
    }
}

function displayNewsContent(container, news) {
    if (news.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-4">No news articles found</div>';
        return;
    }
    
    let html = '';
    news.forEach(article => {
        const sentimentClass = article.sentiment === 'positive' ? 'badge-success' : 
                              article.sentiment === 'negative' ? 'badge-danger' : 'badge-secondary';
        
        html += `
            <div class="card mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0">
                            <a href="${article.link}" target="_blank" class="text-decoration-none">${article.title}</a>
                        </h6>
                        <span class="badge ${sentimentClass}">${article.sentiment}</span>
                    </div>
                    <p class="card-text">${article.summary}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">${article.source} • ${new Date(article.published_at).toLocaleDateString()}</small>
                        <span class="badge badge-info">${article.category}</span>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

function displayTechnicalContent(container, indicators) {
    if (Object.keys(indicators).length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-4">No technical indicators available</div>';
        return;
    }
    
    let html = '<div class="row">';
    
    Object.entries(indicators).forEach(([key, indicator]) => {
        const signalClass = indicator.signal === 'buy' ? 'text-success' : 
                           indicator.signal === 'sell' ? 'text-danger' : 'text-secondary';
        
        html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">${key.toUpperCase()}</h6>
                        <div class="h4 mb-2">${indicator.value.toFixed(2)}</div>
                        <div class="progress mb-2" style="height: 8px;">
                            <div class="progress-bar" role="progressbar" style="width: ${indicator.signal_strength}%"></div>
                        </div>
                        <small class="${signalClass}">${indicator.signal.toUpperCase()}</small>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

function displayEventsContent(container, events) {
    if (events.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-4">No upcoming events</div>';
        return;
    }
    
    let html = '';
    events.forEach(event => {
        const impactClass = event.impact_level === 'high' ? 'badge-warning' : 
                           event.impact_level === 'critical' ? 'badge-danger' : 'badge-info';
        
        html += `
            <div class="card mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0">${event.title}</h6>
                        <span class="badge ${impactClass}">${event.impact_level}</span>
                    </div>
                    <p class="card-text">${event.description}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fa fa-calendar mr-1"></i>
                            ${new Date(event.date).toLocaleDateString()}
                        </small>
                        <span class="badge badge-secondary">${event.event_type}</span>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Utility Functions
function showSuccessMessage(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    const container = document.querySelector('.container').firstElementChild;
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert-success');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

function showErrorMessage(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    const container = document.querySelector('.container').firstElementChild;
    container.insertAdjacentHTML('afterbegin', alertHtml);
}

function initTooltips() {
    // Initialize Bootstrap tooltips
    if (typeof $ !== 'undefined' && $.fn.tooltip) {
        $('[data-toggle="tooltip"]').tooltip();
    }
}

function initAjaxForms() {
    // Initialize AJAX form submissions
    const ajaxForms = document.querySelectorAll('.ajax-form');
    ajaxForms.forEach(form => {
        form.addEventListener('submit', handleAjaxForm);
    });
}

function handleAjaxForm(e) {
    e.preventDefault();
    // AJAX form handling implementation
    console.log('AJAX form submitted');
}
