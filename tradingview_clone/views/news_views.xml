<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- News Tree View -->
        <record id="view_tradingview_news_tree" model="ir.ui.view">
            <field name="name">tradingview.news.tree</field>
            <field name="model">tradingview.news</field>
            <field name="arch" type="xml">
                <tree string="Financial News" default_order="published_at desc">
                    <field name="published_at" string="Published"/>
                    <field name="symbol_id" string="Symbol"/>
                    <field name="title" string="Title"/>
                    <field name="source" string="Source"/>
                    <field name="category" string="Category"/>
                    <field name="sentiment" string="Sentiment" 
                           decoration-success="sentiment in ('positive', 'very_positive')" 
                           decoration-danger="sentiment in ('negative', 'very_negative')"/>
                    <field name="impact_level" string="Impact"/>
                    <field name="views_count" string="Views" widget="integer"/>
                    <field name="is_featured" string="Featured" widget="boolean_toggle"/>
                    <field name="is_breaking" string="Breaking" widget="boolean_toggle"/>
                    <field name="active" string="Active" widget="boolean_toggle"/>
                </tree>
            </field>
        </record>
        
        <!-- News Form View -->
        <record id="view_tradingview_news_form" model="ir.ui.view">
            <field name="name">tradingview.news.form</field>
            <field name="model">tradingview.news</field>
            <field name="arch" type="xml">
                <form string="Financial News Article">
                    <header>
                        <button name="toggle_featured" type="object" string="Toggle Featured" class="btn-secondary"/>
                        <button name="mark_as_breaking" type="object" string="Mark as Breaking" class="btn-warning" 
                                attrs="{'invisible': [('is_breaking', '=', True)]}"/>
                        <field name="active" widget="boolean_toggle"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" icon="fa-eye">
                                <field name="views_count" widget="statinfo" string="Views"/>
                            </button>
                            <button class="oe_stat_button" icon="fa-thumbs-up">
                                <field name="likes_count" widget="statinfo" string="Likes"/>
                            </button>
                            <button class="oe_stat_button" icon="fa-share">
                                <field name="shares_count" widget="statinfo" string="Shares"/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="title" placeholder="Article Title"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="symbol_id"/>
                                <field name="source"/>
                                <field name="author"/>
                                <field name="published_at"/>
                                <field name="link" widget="url"/>
                            </group>
                            <group name="classification" string="Classification">
                                <field name="category"/>
                                <field name="sentiment"/>
                                <field name="impact_level"/>
                                <field name="language"/>
                                <field name="keywords"/>
                            </group>
                        </group>
                        
                        <group>
                            <group name="flags" string="Flags">
                                <field name="is_featured"/>
                                <field name="is_breaking"/>
                                <field name="is_verified"/>
                                <field name="is_recent"/>
                            </group>
                            <group name="metrics" string="Metrics">
                                <field name="word_count"/>
                                <field name="age_hours"/>
                                <field name="comments_count"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Summary" name="summary">
                                <field name="summary" placeholder="Article summary or excerpt"/>
                            </page>
                            <page string="Full Content" name="content">
                                <field name="content" widget="html" placeholder="Full article content (if available)"/>
                            </page>
                            <page string="Source Information" name="source_info">
                                <group>
                                    <field name="source_logo" widget="url"/>
                                    <field name="created_date"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>
        
        <!-- News Search View -->
        <record id="view_tradingview_news_search" model="ir.ui.view">
            <field name="name">tradingview.news.search</field>
            <field name="model">tradingview.news</field>
            <field name="arch" type="xml">
                <search string="Search News">
                    <field name="title" string="Title" filter_domain="[('title', 'ilike', self)]"/>
                    <field name="symbol_id" string="Symbol"/>
                    <field name="source"/>
                    <field name="author"/>
                    <field name="keywords" string="Keywords" filter_domain="[('keywords', 'ilike', self)]"/>
                    
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Featured" name="featured" domain="[('is_featured', '=', True)]"/>
                    <filter string="Breaking News" name="breaking" domain="[('is_breaking', '=', True)]"/>
                    <filter string="Recent" name="recent" domain="[('is_recent', '=', True)]"/>
                    
                    <separator/>
                    <filter string="Today" name="today" 
                            domain="[('published_at', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))),
                                     ('published_at', '&lt;', datetime.datetime.combine(context_today() + datetime.timedelta(days=1), datetime.time(0,0,0)))]"/>
                    <filter string="This Week" name="this_week" 
                            domain="[('published_at', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                    <filter string="This Month" name="this_month" 
                            domain="[('published_at', '>=', context_today().strftime('%Y-%m-01'))]"/>
                    
                    <separator/>
                    <filter string="Positive Sentiment" name="positive" domain="[('sentiment', 'in', ['positive', 'very_positive'])]"/>
                    <filter string="Negative Sentiment" name="negative" domain="[('sentiment', 'in', ['negative', 'very_negative'])]"/>
                    <filter string="Neutral Sentiment" name="neutral" domain="[('sentiment', '=', 'neutral')]"/>
                    
                    <separator/>
                    <filter string="High Impact" name="high_impact" domain="[('impact_level', 'in', ['high', 'critical'])]"/>
                    <filter string="Medium Impact" name="medium_impact" domain="[('impact_level', '=', 'medium')]"/>
                    <filter string="Low Impact" name="low_impact" domain="[('impact_level', '=', 'low')]"/>
                    
                    <separator/>
                    <filter string="Earnings" name="earnings" domain="[('category', '=', 'earnings')]"/>
                    <filter string="Market Analysis" name="market" domain="[('category', '=', 'market')]"/>
                    <filter string="Company News" name="company" domain="[('category', '=', 'company')]"/>
                    <filter string="Crypto" name="crypto" domain="[('category', '=', 'crypto')]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Symbol" name="group_symbol" context="{'group_by': 'symbol_id'}"/>
                        <filter string="Source" name="group_source" context="{'group_by': 'source'}"/>
                        <filter string="Category" name="group_category" context="{'group_by': 'category'}"/>
                        <filter string="Sentiment" name="group_sentiment" context="{'group_by': 'sentiment'}"/>
                        <filter string="Impact Level" name="group_impact" context="{'group_by': 'impact_level'}"/>
                        <filter string="Published Date" name="group_date" context="{'group_by': 'published_at:day'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- News Kanban View -->
        <record id="view_tradingview_news_kanban" model="ir.ui.view">
            <field name="name">tradingview.news.kanban</field>
            <field name="model">tradingview.news</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="title"/>
                    <field name="symbol_id"/>
                    <field name="source"/>
                    <field name="published_at"/>
                    <field name="sentiment"/>
                    <field name="impact_level"/>
                    <field name="is_breaking"/>
                    <field name="is_featured"/>
                    <field name="views_count"/>
                    <field name="display_summary"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="title"/>
                                            <t t-if="record.is_breaking.raw_value">
                                                <span class="badge badge-danger ml-2">BREAKING</span>
                                            </t>
                                            <t t-if="record.is_featured.raw_value">
                                                <span class="badge badge-warning ml-2">FEATURED</span>
                                            </t>
                                        </strong>
                                        <div class="o_kanban_record_subtitle">
                                            <field name="symbol_id"/> • <field name="source"/> • <field name="published_at"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div class="row">
                                            <div class="col-12">
                                                <p><field name="display_summary"/></p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                <span t-attf-class="badge badge-pill #{record.sentiment.raw_value === 'positive' or record.sentiment.raw_value === 'very_positive' ? 'badge-success' : record.sentiment.raw_value === 'negative' or record.sentiment.raw_value === 'very_negative' ? 'badge-danger' : 'badge-secondary'}">
                                                    <field name="sentiment"/>
                                                </span>
                                            </div>
                                            <div class="col-6 text-right">
                                                <span t-attf-class="badge badge-pill #{record.impact_level.raw_value === 'critical' ? 'badge-danger' : record.impact_level.raw_value === 'high' ? 'badge-warning' : record.impact_level.raw_value === 'medium' ? 'badge-info' : 'badge-secondary'}">
                                                    <field name="impact_level"/>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <small class="text-muted">
                                                    <i class="fa fa-eye"/> <field name="views_count"/> views
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- Action -->
        <record id="action_tradingview_news" model="ir.actions.act_window">
            <field name="name">Financial News</field>
            <field name="res_model">tradingview.news</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="context">{'search_default_active': 1, 'search_default_today': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No news articles found!
                </p>
                <p>
                    Financial news articles will appear here once you start synchronizing news data from various sources.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
