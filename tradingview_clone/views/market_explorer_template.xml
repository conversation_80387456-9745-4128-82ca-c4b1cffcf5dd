<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Market Explorer Template -->
        <template id="market_explorer_template" name="Market Explorer">
            <t t-call="website.layout">
                <t t-set="title" t-value="'Market Explorer'"/>
                <t t-set="additional_title" t-value="'Financial Symbols &amp; Market Data'"/>
                
                <!-- <PERSON>er -->
                <div class="container-fluid bg-primary text-white py-4">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h1 class="mb-2">Market Explorer</h1>
                                <p class="mb-0">Discover and track financial symbols across all markets</p>
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="d-flex flex-column align-items-end">
                                    <div class="h5 mb-1">
                                        <span t-esc="total_symbols"/> Symbols
                                    </div>
                                    <div t-if="market_status">
                                        <span t-attf-class="badge badge-lg #{market_status['is_open'] and 'badge-success' or 'badge-secondary'}">
                                            Market <span t-esc="market_status['status']"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="container-fluid">
                    <div class="row">
                        <!-- Filters Sidebar -->
                        <div class="col-lg-3">
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Filters</h6>
                                </div>
                                <div class="card-body">
                                    <form method="get" id="filter-form">
                                        <!-- Search -->
                                        <div class="form-group">
                                            <label for="search">Search</label>
                                            <input type="text" class="form-control" id="search" name="search" 
                                                   t-att-value="search" placeholder="Symbol or company name"/>
                                        </div>
                                        
                                        <!-- Symbol Type -->
                                        <div class="form-group">
                                            <label for="symbol_type">Symbol Type</label>
                                            <select class="form-control" id="symbol_type" name="symbol_type">
                                                <option value="">All Types</option>
                                                <option t-foreach="filter_options['types']" t-as="type_option" 
                                                        t-att-value="type_option['key']" 
                                                        t-att-selected="symbol_type == type_option['key'] and 'selected' or None">
                                                    <t t-esc="type_option['key'].title()"/> (<t t-esc="type_option['count']"/>)
                                                </option>
                                            </select>
                                        </div>
                                        
                                        <!-- Exchange -->
                                        <div class="form-group">
                                            <label for="exchange">Exchange</label>
                                            <select class="form-control" id="exchange" name="exchange">
                                                <option value="">All Exchanges</option>
                                                <option t-foreach="filter_options['exchanges']" t-as="exchange_option" 
                                                        t-att-value="exchange_option" 
                                                        t-att-selected="exchange == exchange_option and 'selected' or None"
                                                        t-esc="exchange_option"/>
                                            </select>
                                        </div>
                                        
                                        <!-- Region -->
                                        <div class="form-group">
                                            <label for="region">Region</label>
                                            <select class="form-control" id="region" name="region">
                                                <option value="">All Regions</option>
                                                <option t-foreach="filter_options['regions']" t-as="region_option" 
                                                        t-att-value="region_option" 
                                                        t-att-selected="region == region_option and 'selected' or None"
                                                        t-esc="region_option"/>
                                            </select>
                                        </div>
                                        
                                        <!-- Sector -->
                                        <div class="form-group">
                                            <label for="sector">Sector</label>
                                            <select class="form-control" id="sector" name="sector">
                                                <option value="">All Sectors</option>
                                                <option t-foreach="filter_options['sectors']" t-as="sector_option" 
                                                        t-att-value="sector_option" 
                                                        t-att-selected="sector == sector_option and 'selected' or None"
                                                        t-esc="sector_option"/>
                                            </select>
                                        </div>
                                        
                                        <!-- Sort Options -->
                                        <div class="form-group">
                                            <label for="sort_by">Sort By</label>
                                            <select class="form-control" id="sort_by" name="sort_by">
                                                <option value="symbol" t-att-selected="sort_by == 'symbol' and 'selected' or None">Symbol</option>
                                                <option value="name" t-att-selected="sort_by == 'name' and 'selected' or None">Name</option>
                                                <option value="current_price" t-att-selected="sort_by == 'current_price' and 'selected' or None">Price</option>
                                                <option value="daily_change_percent" t-att-selected="sort_by == 'daily_change_percent' and 'selected' or None">Daily Change</option>
                                                <option value="volume" t-att-selected="sort_by == 'volume' and 'selected' or None">Volume</option>
                                                <option value="market_cap" t-att-selected="sort_by == 'market_cap' and 'selected' or None">Market Cap</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="sort_order">Sort Order</label>
                                            <select class="form-control" id="sort_order" name="sort_order">
                                                <option value="asc" t-att-selected="sort_order == 'asc' and 'selected' or None">Ascending</option>
                                                <option value="desc" t-att-selected="sort_order == 'desc' and 'selected' or None">Descending</option>
                                            </select>
                                        </div>
                                        
                                        <button type="submit" class="btn btn-primary btn-block">Apply Filters</button>
                                        <a href="/market" class="btn btn-outline-secondary btn-block">Clear All</a>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Main Content -->
                        <div class="col-lg-9">
                            <!-- Search Bar and Controls -->
                            <div class="d-flex justify-content-between align-items-center my-4">
                                <div class="d-flex align-items-center">
                                    <h4 class="mb-0 mr-3">Symbols</h4>
                                    <span class="text-muted">
                                        Showing <strong t-esc="(page - 1) * symbols_per_page + 1"/>-<strong t-esc="min(page * symbols_per_page, total_symbols)"/> 
                                        of <strong t-esc="total_symbols"/> symbols
                                    </span>
                                </div>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary active" id="table-view-btn">
                                        <i class="fa fa-table"/> Table
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="card-view-btn">
                                        <i class="fa fa-th-large"/> Cards
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Symbols Table View -->
                            <div id="table-view" class="card">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>Symbol</th>
                                                <th>Name</th>
                                                <th>Type</th>
                                                <th>Exchange</th>
                                                <th class="text-right">Price</th>
                                                <th class="text-right">Change</th>
                                                <th class="text-right">Change %</th>
                                                <th class="text-right">Volume</th>
                                                <th class="text-right">Market Cap</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr t-foreach="symbols" t-as="symbol" class="clickable-row" t-attf-data-href="/market/#{symbol.slug}">
                                                <td>
                                                    <a t-attf-href="/market/#{symbol.slug}" class="font-weight-bold text-decoration-none">
                                                        <t t-esc="symbol.symbol"/>
                                                    </a>
                                                </td>
                                                <td>
                                                    <span t-esc="symbol.name"/>
                                                </td>
                                                <td>
                                                    <span class="badge badge-info" t-esc="symbol.type.title()"/>
                                                </td>
                                                <td t-esc="symbol.exchange"/>
                                                <td class="text-right">
                                                    <span t-if="symbol.current_price" t-esc="symbol.current_price" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                                    <span t-else="">--</span>
                                                </td>
                                                <td class="text-right">
                                                    <span t-if="symbol.daily_change" 
                                                          t-attf-class="#{symbol.daily_change >= 0 and 'text-success' or 'text-danger'}"
                                                          t-esc="symbol.daily_change" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                                    <span t-else="">--</span>
                                                </td>
                                                <td class="text-right">
                                                    <span t-if="symbol.daily_change_percent" 
                                                          t-attf-class="#{symbol.daily_change_percent >= 0 and 'text-success' or 'text-danger'}">
                                                        <t t-esc="symbol.daily_change_percent"/>%
                                                    </span>
                                                    <span t-else="">--</span>
                                                </td>
                                                <td class="text-right">
                                                    <span t-if="symbol.volume" t-esc="symbol.volume" t-options="{'widget': 'integer'}"/>
                                                    <span t-else="">--</span>
                                                </td>
                                                <td class="text-right">
                                                    <span t-if="symbol.market_cap" t-esc="symbol.market_cap" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                                    <span t-else="">--</span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- Symbols Card View -->
                            <div id="card-view" class="d-none">
                                <div class="row">
                                    <div t-foreach="symbols" t-as="symbol" class="col-md-6 col-lg-4 mb-4">
                                        <div class="card h-100 symbol-card" t-attf-data-href="/market/#{symbol.slug}">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <div>
                                                        <h6 class="card-title mb-1">
                                                            <a t-attf-href="/market/#{symbol.slug}" class="text-decoration-none" t-esc="symbol.symbol"/>
                                                        </h6>
                                                        <small class="text-muted" t-esc="symbol.name"/>
                                                    </div>
                                                    <span class="badge badge-info" t-esc="symbol.type.title()"/>
                                                </div>
                                                
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <div>
                                                        <div class="h5 mb-0">
                                                            <span t-if="symbol.current_price" t-esc="symbol.current_price" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                                            <span t-else="">--</span>
                                                        </div>
                                                        <small class="text-muted" t-esc="symbol.exchange"/>
                                                    </div>
                                                    <div class="text-right">
                                                        <div t-if="symbol.daily_change" 
                                                             t-attf-class="#{symbol.daily_change >= 0 and 'text-success' or 'text-danger'}">
                                                            <span t-esc="symbol.daily_change" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                                        </div>
                                                        <div t-if="symbol.daily_change_percent" 
                                                             t-attf-class="#{symbol.daily_change_percent >= 0 and 'text-success' or 'text-danger'}">
                                                            <t t-esc="symbol.daily_change_percent"/>%
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row text-center small text-muted">
                                                    <div class="col-6">
                                                        <div>Volume</div>
                                                        <div t-if="symbol.volume" t-esc="symbol.volume" t-options="{'widget': 'integer'}"/>
                                                        <div t-else="">--</div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div>Market Cap</div>
                                                        <div t-if="symbol.market_cap" t-esc="symbol.market_cap" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                                        <div t-else="">--</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Pagination -->
                            <nav t-if="total_pages > 1" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <!-- Previous Page -->
                                    <li t-attf-class="page-item #{page <= 1 and 'disabled' or ''}">
                                        <a class="page-link" t-attf-href="/market/page/#{page - 1}?#{keep_query()}">Previous</a>
                                    </li>
                                    
                                    <!-- Page Numbers -->
                                    <t t-set="start_page" t-value="max(1, page - 2)"/>
                                    <t t-set="end_page" t-value="min(total_pages, page + 2)"/>
                                    
                                    <li t-if="start_page > 1" class="page-item">
                                        <a class="page-link" t-attf-href="/market/page/1?#{keep_query()}">1</a>
                                    </li>
                                    <li t-if="start_page > 2" class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    
                                    <t t-foreach="range(start_page, end_page + 1)" t-as="page_num">
                                        <li t-attf-class="page-item #{page_num == page and 'active' or ''}">
                                            <a class="page-link" t-attf-href="/market/page/#{page_num}?#{keep_query()}" t-esc="page_num"/>
                                        </li>
                                    </t>
                                    
                                    <li t-if="end_page < total_pages - 1" class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    <li t-if="end_page < total_pages" class="page-item">
                                        <a class="page-link" t-attf-href="/market/page/#{total_pages}?#{keep_query()}" t-esc="total_pages"/>
                                    </li>
                                    
                                    <!-- Next Page -->
                                    <li t-attf-class="page-item #{page >= total_pages and 'disabled' or ''}">
                                        <a class="page-link" t-attf-href="/market/page/#{page + 1}?#{keep_query()}">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
                
                <!-- JavaScript for interactions -->
                <script type="text/javascript">
                    document.addEventListener('DOMContentLoaded', function() {
                        // Initialize market explorer functionality
                        initMarketExplorer();
                        
                        // Initialize view switching
                        initViewSwitching();
                        
                        // Initialize clickable rows
                        initClickableRows();
                    });
                    
                    function keep_query() {
                        var params = new URLSearchParams();
                        if ('<t t-esc="search"/>') params.set('search', '<t t-esc="search"/>');
                        if ('<t t-esc="symbol_type"/>') params.set('symbol_type', '<t t-esc="symbol_type"/>');
                        if ('<t t-esc="exchange"/>') params.set('exchange', '<t t-esc="exchange"/>');
                        if ('<t t-esc="region"/>') params.set('region', '<t t-esc="region"/>');
                        if ('<t t-esc="sector"/>') params.set('sector', '<t t-esc="sector"/>');
                        if ('<t t-esc="sort_by"/>') params.set('sort_by', '<t t-esc="sort_by"/>');
                        if ('<t t-esc="sort_order"/>') params.set('sort_order', '<t t-esc="sort_order"/>');
                        return params.toString();
                    }
                </script>
            </t>
        </template>
        
    </data>
</odoo>
