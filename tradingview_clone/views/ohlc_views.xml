<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- OHLC Tree View -->
        <record id="view_tradingview_ohlc_tree" model="ir.ui.view">
            <field name="name">tradingview.ohlc.tree</field>
            <field name="model">tradingview.ohlc</field>
            <field name="arch" type="xml">
                <tree string="OHLC Data" default_order="timestamp desc">
                    <field name="symbol_id" string="Symbol"/>
                    <field name="timestamp" string="Timestamp"/>
                    <field name="timeframe" string="Timeframe"/>
                    <field name="open" string="Open" widget="monetary"/>
                    <field name="high" string="High" widget="monetary"/>
                    <field name="low" string="Low" widget="monetary"/>
                    <field name="close" string="Close" widget="monetary"/>
                    <field name="volume" string="Volume" widget="integer"/>
                    <field name="price_change" string="Change" decoration-success="price_change > 0" decoration-danger="price_change &lt; 0"/>
                    <field name="price_change_percent" string="Change %" decoration-success="price_change_percent > 0" decoration-danger="price_change_percent &lt; 0"/>
                    <field name="is_bullish" string="Bullish" widget="boolean_toggle"/>
                    <field name="data_source" string="Source"/>
                </tree>
            </field>
        </record>
        
        <!-- OHLC Form View -->
        <record id="view_tradingview_ohlc_form" model="ir.ui.view">
            <field name="name">tradingview.ohlc.form</field>
            <field name="model">tradingview.ohlc</field>
            <field name="arch" type="xml">
                <form string="OHLC Data">
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="display_name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="symbol_id"/>
                                <field name="timestamp"/>
                                <field name="timeframe"/>
                                <field name="data_source"/>
                            </group>
                            <group name="computed" string="Computed Values">
                                <field name="price_change" decoration-success="price_change > 0" decoration-danger="price_change &lt; 0"/>
                                <field name="price_change_percent" decoration-success="price_change_percent > 0" decoration-danger="price_change_percent &lt; 0"/>
                                <field name="price_range"/>
                                <field name="is_bullish" widget="boolean_toggle"/>
                            </group>
                        </group>
                        
                        <group>
                            <group name="ohlc_prices" string="OHLC Prices">
                                <field name="open" widget="monetary"/>
                                <field name="high" widget="monetary"/>
                                <field name="low" widget="monetary"/>
                                <field name="close" widget="monetary"/>
                            </group>
                            <group name="volume_data" string="Volume Data">
                                <field name="volume" widget="integer"/>
                                <field name="volume_weighted_price" widget="monetary"/>
                                <field name="trades_count"/>
                            </group>
                        </group>
                        
                        <group>
                            <field name="created_date"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- OHLC Search View -->
        <record id="view_tradingview_ohlc_search" model="ir.ui.view">
            <field name="name">tradingview.ohlc.search</field>
            <field name="model">tradingview.ohlc</field>
            <field name="arch" type="xml">
                <search string="Search OHLC Data">
                    <field name="symbol_id" string="Symbol"/>
                    <field name="timestamp"/>
                    <field name="timeframe"/>
                    <field name="data_source"/>
                    
                    <filter string="Today" name="today" 
                            domain="[('timestamp', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))),
                                     ('timestamp', '&lt;', datetime.datetime.combine(context_today() + datetime.timedelta(days=1), datetime.time(0,0,0)))]"/>
                    <filter string="This Week" name="this_week" 
                            domain="[('timestamp', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')),
                                     ('timestamp', '&lt;', (context_today() + datetime.timedelta(days=7-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                    <filter string="This Month" name="this_month" 
                            domain="[('timestamp', '>=', context_today().strftime('%Y-%m-01'))]"/>
                    
                    <separator/>
                    <filter string="Bullish" name="bullish" domain="[('is_bullish', '=', True)]"/>
                    <filter string="Bearish" name="bearish" domain="[('is_bullish', '=', False)]"/>
                    <filter string="High Volume" name="high_volume" domain="[('volume', '>', 1000000)]"/>
                    
                    <separator/>
                    <filter string="1 Minute" name="1m" domain="[('timeframe', '=', '1m')]"/>
                    <filter string="5 Minutes" name="5m" domain="[('timeframe', '=', '5m')]"/>
                    <filter string="15 Minutes" name="15m" domain="[('timeframe', '=', '15m')]"/>
                    <filter string="1 Hour" name="1h" domain="[('timeframe', '=', '1h')]"/>
                    <filter string="1 Day" name="1d" domain="[('timeframe', '=', '1d')]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Symbol" name="group_symbol" context="{'group_by': 'symbol_id'}"/>
                        <filter string="Timeframe" name="group_timeframe" context="{'group_by': 'timeframe'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'timestamp:day'}"/>
                        <filter string="Data Source" name="group_source" context="{'group_by': 'data_source'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- OHLC Graph View -->
        <record id="view_tradingview_ohlc_graph" model="ir.ui.view">
            <field name="name">tradingview.ohlc.graph</field>
            <field name="model">tradingview.ohlc</field>
            <field name="arch" type="xml">
                <graph string="OHLC Chart" type="line">
                    <field name="timestamp" type="row" interval="day"/>
                    <field name="close" type="measure"/>
                    <field name="volume" type="measure"/>
                </graph>
            </field>
        </record>
        
        <!-- OHLC Pivot View -->
        <record id="view_tradingview_ohlc_pivot" model="ir.ui.view">
            <field name="name">tradingview.ohlc.pivot</field>
            <field name="model">tradingview.ohlc</field>
            <field name="arch" type="xml">
                <pivot string="OHLC Analysis">
                    <field name="symbol_id" type="row"/>
                    <field name="timeframe" type="col"/>
                    <field name="close" type="measure"/>
                    <field name="volume" type="measure"/>
                    <field name="price_change" type="measure"/>
                </pivot>
            </field>
        </record>
        
        <!-- Action -->
        <record id="action_tradingview_ohlc" model="ir.actions.act_window">
            <field name="name">OHLC Data</field>
            <field name="res_model">tradingview.ohlc</field>
            <field name="view_mode">tree,form,graph,pivot</field>
            <field name="context">{'search_default_today': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No OHLC data found!
                </p>
                <p>
                    OHLC (Open, High, Low, Close) data will appear here once you start synchronizing market data.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
