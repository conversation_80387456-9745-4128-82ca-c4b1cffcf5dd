<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Symbol Detail Page Template -->
        <template id="symbol_detail_template" name="Symbol Detail Page">
            <t t-call="website.layout">
                <t t-set="title" t-value="symbol.symbol + ' - ' + symbol.name"/>
                <t t-set="additional_title" t-value="'Financial Data &amp; Analysis'"/>
                
                <!-- <PERSON> Header -->
                <div class="container-fluid bg-light py-3">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <h1 class="mb-0 mr-3">
                                        <span class="font-weight-bold" t-esc="symbol.symbol"/>
                                        <small class="text-muted ml-2" t-esc="symbol.name"/>
                                    </h1>
                                    <span class="badge badge-info mr-2" t-esc="symbol.type.title()"/>
                                    <span class="badge badge-secondary" t-esc="symbol.exchange"/>
                                </div>
                                <div class="text-muted">
                                    <span t-if="symbol.region" t-esc="symbol.region"/> • 
                                    <span t-if="symbol.currency" t-esc="symbol.currency"/> • 
                                    <span t-if="market_status">Market <span t-esc="market_status['status']"/></span>
                                </div>
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="d-flex flex-column align-items-end">
                                    <h2 class="mb-1 font-weight-bold">
                                        <span t-if="symbol.current_price" t-esc="symbol.current_price" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                        <span t-else="">--</span>
                                    </h2>
                                    <div t-if="symbol.daily_change or symbol.daily_change_percent">
                                        <span t-attf-class="badge badge-lg #{symbol.daily_change >= 0 and 'badge-success' or 'badge-danger'}">
                                            <span t-esc="symbol.daily_change" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                            (<span t-esc="symbol.daily_change_percent"/>%)
                                        </span>
                                    </div>
                                    <small class="text-muted" t-if="symbol.last_updated">
                                        Last updated: <span t-esc="symbol.last_updated" t-options="{'widget': 'datetime'}"/>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="container-fluid">
                    <div class="row">
                        <!-- Main Content -->
                        <div class="col-lg-9">
                            <!-- Chart Section -->
                            <div class="card mb-4">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Price Chart</h5>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-secondary active" data-timeframe="1d">1D</button>
                                        <button type="button" class="btn btn-outline-secondary" data-timeframe="1w">1W</button>
                                        <button type="button" class="btn btn-outline-secondary" data-timeframe="1M">1M</button>
                                        <button type="button" class="btn btn-outline-secondary" data-timeframe="3M">3M</button>
                                        <button type="button" class="btn btn-outline-secondary" data-timeframe="1Y">1Y</button>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <div id="tradingview-chart" style="height: 400px;">
                                        <!-- TradingView Chart will be loaded here -->
                                        <div class="d-flex justify-content-center align-items-center h-100">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="sr-only">Loading chart...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Tabs Section -->
                            <div class="card">
                                <div class="card-header">
                                    <ul class="nav nav-tabs card-header-tabs" id="symbol-tabs" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="overview-tab" data-toggle="tab" href="#overview" role="tab">Overview</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="news-tab" data-toggle="tab" href="#news" role="tab">News</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="technicals-tab" data-toggle="tab" href="#technicals" role="tab">Technicals</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="events-tab" data-toggle="tab" href="#events" role="tab">Events</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="card-body">
                                    <div class="tab-content" id="symbol-tab-content">
                                        <!-- Overview Tab -->
                                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                                            <t t-call="tradingview_clone.symbol_overview_tab"/>
                                        </div>
                                        
                                        <!-- News Tab -->
                                        <div class="tab-pane fade" id="news" role="tabpanel">
                                            <t t-call="tradingview_clone.symbol_news_tab"/>
                                        </div>
                                        
                                        <!-- Technicals Tab -->
                                        <div class="tab-pane fade" id="technicals" role="tabpanel">
                                            <t t-call="tradingview_clone.symbol_technicals_tab"/>
                                        </div>
                                        
                                        <!-- Events Tab -->
                                        <div class="tab-pane fade" id="events" role="tabpanel">
                                            <t t-call="tradingview_clone.symbol_events_tab"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Sidebar -->
                        <div class="col-lg-3">
                            <t t-call="tradingview_clone.symbol_sidebar"/>
                        </div>
                    </div>
                </div>
                
                <!-- JavaScript for chart and interactions -->
                <script type="text/javascript">
                    document.addEventListener('DOMContentLoaded', function() {
                        // Initialize TradingView chart
                        initTradingViewChart('<t t-esc="symbol.symbol"/>', '<t t-esc="symbol.slug"/>');
                        
                        // Initialize watchlist functionality
                        initWatchlistButtons('<t t-esc="symbol.slug"/>', <t t-esc="in_watchlist and 'true' or 'false'"/>);
                        
                        // Initialize tab content loading
                        initTabContentLoading('<t t-esc="symbol.slug"/>');
                    });
                </script>
            </t>
        </template>
        
        <!-- Symbol Overview Tab -->
        <template id="symbol_overview_tab" name="Symbol Overview Tab">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="font-weight-bold mb-3">Key Statistics</h6>
                    <table class="table table-sm">
                        <tbody>
                            <tr t-if="symbol.market_cap">
                                <td>Market Cap</td>
                                <td class="text-right font-weight-bold">
                                    <span t-esc="symbol.market_cap" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                </td>
                            </tr>
                            <tr t-if="symbol.volume">
                                <td>Volume</td>
                                <td class="text-right font-weight-bold" t-esc="symbol.volume" t-options="{'widget': 'integer'}"/>
                            </tr>
                            <tr t-if="symbol.day_high">
                                <td>Day High</td>
                                <td class="text-right font-weight-bold">
                                    <span t-esc="symbol.day_high" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                </td>
                            </tr>
                            <tr t-if="symbol.day_low">
                                <td>Day Low</td>
                                <td class="text-right font-weight-bold">
                                    <span t-esc="symbol.day_low" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                </td>
                            </tr>
                            <tr t-if="symbol.week_52_high">
                                <td>52W High</td>
                                <td class="text-right font-weight-bold">
                                    <span t-esc="symbol.week_52_high" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                </td>
                            </tr>
                            <tr t-if="symbol.week_52_low">
                                <td>52W Low</td>
                                <td class="text-right font-weight-bold">
                                    <span t-esc="symbol.week_52_low" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="font-weight-bold mb-3">Company Information</h6>
                    <table class="table table-sm">
                        <tbody>
                            <tr t-if="symbol.sector">
                                <td>Sector</td>
                                <td class="text-right" t-esc="symbol.sector"/>
                            </tr>
                            <tr t-if="symbol.industry">
                                <td>Industry</td>
                                <td class="text-right" t-esc="symbol.industry"/>
                            </tr>
                            <tr t-if="symbol.region">
                                <td>Region</td>
                                <td class="text-right" t-esc="symbol.region"/>
                            </tr>
                            <tr t-if="symbol.currency">
                                <td>Currency</td>
                                <td class="text-right" t-esc="symbol.currency"/>
                            </tr>
                            <tr t-if="symbol.isin">
                                <td>ISIN</td>
                                <td class="text-right" t-esc="symbol.isin"/>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div t-if="symbol.website" class="mt-3">
                        <a t-att-href="symbol.website" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fa fa-external-link"/> Visit Website
                        </a>
                    </div>
                </div>
            </div>
            
            <div t-if="symbol.description" class="mt-4">
                <h6 class="font-weight-bold mb-3">About</h6>
                <p t-esc="symbol.description"/>
            </div>
        </template>
        
        <!-- Symbol News Tab -->
        <template id="symbol_news_tab" name="Symbol News Tab">
            <div id="news-content">
                <div class="d-flex justify-content-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading news...</span>
                    </div>
                </div>
            </div>
        </template>
        
        <!-- Symbol Technicals Tab -->
        <template id="symbol_technicals_tab" name="Symbol Technicals Tab">
            <div id="technicals-content">
                <div class="d-flex justify-content-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading technical indicators...</span>
                    </div>
                </div>
            </div>
        </template>
        
        <!-- Symbol Events Tab -->
        <template id="symbol_events_tab" name="Symbol Events Tab">
            <div id="events-content">
                <div class="d-flex justify-content-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading events...</span>
                    </div>
                </div>
            </div>
        </template>
        
        <!-- Symbol Sidebar -->
        <template id="symbol_sidebar" name="Symbol Sidebar">
            <!-- Watchlist Actions -->
            <div class="card mb-4">
                <div class="card-body">
                    <div t-if="request.env.user and not request.env.user._is_public()">
                        <button t-if="not in_watchlist" 
                                class="btn btn-primary btn-block" 
                                id="add-to-watchlist-btn">
                            <i class="fa fa-star"/> Add to Watchlist
                        </button>
                        <button t-if="in_watchlist" 
                                class="btn btn-outline-danger btn-block" 
                                id="remove-from-watchlist-btn">
                            <i class="fa fa-star"/> Remove from Watchlist
                        </button>
                    </div>
                    <div t-else="">
                        <a href="/web/login" class="btn btn-outline-primary btn-block">
                            <i class="fa fa-sign-in"/> Login to Add to Watchlist
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Quick Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Quick Stats</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 border-right">
                            <div class="h5 mb-0" t-esc="symbol.ohlc_count"/>
                            <small class="text-muted">OHLC Records</small>
                        </div>
                        <div class="col-6">
                            <div class="h5 mb-0" t-esc="symbol.news_count"/>
                            <small class="text-muted">News Articles</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Upcoming Events -->
            <div class="card mb-4" t-if="upcoming_events">
                <div class="card-header">
                    <h6 class="mb-0">Upcoming Events</h6>
                </div>
                <div class="card-body">
                    <div t-foreach="upcoming_events" t-as="event" class="mb-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="font-weight-bold" t-esc="event.title"/>
                                <small class="text-muted d-block" t-esc="event.date" t-options="{'widget': 'datetime'}"/>
                            </div>
                            <span t-attf-class="badge badge-sm #{event.impact_level == 'high' and 'badge-warning' or event.impact_level == 'critical' and 'badge-danger' or 'badge-info'}" 
                                  t-esc="event.impact_level.title()"/>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Similar Symbols -->
            <div class="card" t-if="similar_symbols">
                <div class="card-header">
                    <h6 class="mb-0">Similar Symbols</h6>
                </div>
                <div class="card-body">
                    <div t-foreach="similar_symbols" t-as="similar" class="mb-2">
                        <a t-attf-href="/market/#{similar.slug}" class="d-flex justify-content-between align-items-center text-decoration-none">
                            <div>
                                <div class="font-weight-bold" t-esc="similar.symbol"/>
                                <small class="text-muted d-block" t-esc="similar.name"/>
                            </div>
                            <div class="text-right">
                                <div t-if="similar.current_price" t-esc="similar.current_price" t-options="{'widget': 'monetary'}"/>
                                <small t-if="similar.daily_change_percent" 
                                       t-attf-class="d-block #{similar.daily_change_percent >= 0 and 'text-success' or 'text-danger'}"
                                       t-esc="similar.daily_change_percent"/>%
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </template>
        
        <!-- Symbol Not Found Template -->
        <template id="symbol_not_found_template" name="Symbol Not Found">
            <t t-call="website.layout">
                <t t-set="title" t-value="'Symbol Not Found'"/>
                
                <div class="container mt-5">
                    <div class="row justify-content-center">
                        <div class="col-md-8 text-center">
                            <h1 class="display-4">Symbol Not Found</h1>
                            <p class="lead">The symbol "<strong t-esc="symbol_slug"/>" could not be found.</p>
                            
                            <div t-if="similar_symbols" class="mt-4">
                                <h5>Did you mean one of these?</h5>
                                <div class="list-group mt-3">
                                    <a t-foreach="similar_symbols" t-as="symbol" 
                                       t-attf-href="/market/#{symbol.slug}" 
                                       class="list-group-item list-group-item-action">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong t-esc="symbol.symbol"/>
                                                <span class="text-muted ml-2" t-esc="symbol.name"/>
                                            </div>
                                            <span class="badge badge-info" t-esc="symbol.type"/>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <a href="/market" class="btn btn-primary">Browse All Symbols</a>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
        
    </data>
</odoo>
