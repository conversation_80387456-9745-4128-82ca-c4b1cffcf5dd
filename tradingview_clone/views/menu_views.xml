<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Main Menu -->
        <menuitem id="menu_tradingview_main"
                  name="Market"
                  sequence="50"
                  web_icon="tradingview_clone,static/description/icon.png"/>
        
        <!-- Symbols Submenu -->
        <menuitem id="menu_tradingview_symbols"
                  name="Symbols"
                  parent="menu_tradingview_main"
                  sequence="10"/>
        
        <menuitem id="menu_tradingview_symbol_list"
                  name="All Symbols"
                  parent="menu_tradingview_symbols"
                  action="action_tradingview_symbol"
                  sequence="10"/>
        
        <menuitem id="menu_tradingview_symbol_stocks"
                  name="Stocks"
                  parent="menu_tradingview_symbols"
                  action="action_tradingview_symbol_stocks"
                  sequence="20"/>
        
        <menuitem id="menu_tradingview_symbol_crypto"
                  name="Cryptocurrency"
                  parent="menu_tradingview_symbols"
                  action="action_tradingview_symbol_crypto"
                  sequence="30"/>
        
        <menuitem id="menu_tradingview_symbol_forex"
                  name="Forex"
                  parent="menu_tradingview_symbols"
                  action="action_tradingview_symbol_forex"
                  sequence="40"/>
        
        <menuitem id="menu_tradingview_symbol_commodities"
                  name="Commodities"
                  parent="menu_tradingview_symbols"
                  action="action_tradingview_symbol_commodities"
                  sequence="50"/>
        
        <!-- Data Submenu -->
        <menuitem id="menu_tradingview_data"
                  name="Market Data"
                  parent="menu_tradingview_main"
                  sequence="20"/>
        
        <menuitem id="menu_tradingview_ohlc"
                  name="OHLC Data"
                  parent="menu_tradingview_data"
                  action="action_tradingview_ohlc"
                  sequence="10"/>
        
        <menuitem id="menu_tradingview_technical"
                  name="Technical Indicators"
                  parent="menu_tradingview_data"
                  action="action_tradingview_technical"
                  sequence="20"/>
        
        <!-- News & Events Submenu -->
        <menuitem id="menu_tradingview_news_events"
                  name="News & Events"
                  parent="menu_tradingview_main"
                  sequence="30"/>
        
        <menuitem id="menu_tradingview_news"
                  name="News Articles"
                  parent="menu_tradingview_news_events"
                  action="action_tradingview_news"
                  sequence="10"/>
        
        <menuitem id="menu_tradingview_events"
                  name="Financial Events"
                  parent="menu_tradingview_news_events"
                  action="action_tradingview_event"
                  sequence="20"/>
        
        <!-- User Features Submenu -->
        <menuitem id="menu_tradingview_users"
                  name="User Features"
                  parent="menu_tradingview_main"
                  sequence="40"/>
        
        <menuitem id="menu_tradingview_watchlist"
                  name="Watchlists"
                  parent="menu_tradingview_users"
                  action="action_tradingview_watchlist"
                  sequence="10"/>
        
        <!-- Administration Submenu -->
        <menuitem id="menu_tradingview_admin"
                  name="Administration"
                  parent="menu_tradingview_main"
                  sequence="50"
                  groups="tradingview_clone.group_tradingview_manager"/>
        
        <menuitem id="menu_tradingview_sync_logs"
                  name="Sync Logs"
                  parent="menu_tradingview_admin"
                  action="action_tradingview_sync_log"
                  sequence="10"
                  groups="tradingview_clone.group_tradingview_manager"/>
        
        <menuitem id="menu_tradingview_sync_wizard"
                  name="Manual Sync"
                  parent="menu_tradingview_admin"
                  action="action_tradingview_sync_wizard"
                  sequence="20"
                  groups="tradingview_clone.group_tradingview_manager"/>
        
        <!-- Dashboard -->
        <menuitem id="menu_tradingview_dashboard"
                  name="Dashboard"
                  parent="menu_tradingview_main"
                  action="action_tradingview_dashboard"
                  sequence="5"/>
        
    </data>
</odoo>
