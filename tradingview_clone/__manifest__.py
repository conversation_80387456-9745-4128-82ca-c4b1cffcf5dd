# -*- coding: utf-8 -*-
{
    'name': 'TradingView Clone',
    'version': '********.0',
    'category': 'Finance',
    'summary': 'Comprehensive financial symbol tracking system with TradingView-style interface',
    'description': """
TradingView Clone - Financial Symbol Tracking System
===================================================

A comprehensive financial symbol tracking system that supports all financial instruments:
- Stocks, Forex, Cryptocurrency, Commodities, Indices
- Real-time price data and OHLC charts
- Technical indicators (RSI, MACD, Moving Averages, etc.)
- News aggregation and financial events
- User watchlists and forum integration
- Dynamic symbol pages with interactive charts

Features:
---------
* Dynamic symbol detail pages (/market/<symbol>)
* Market explorer with advanced search and filtering
* Real-time data synchronization from multiple APIs
* Interactive TradingView-style charts
* Technical analysis tools and indicators
* News aggregation from multiple sources
* User watchlists and portfolio tracking
* Forum integration for symbol discussions
* Mobile-responsive design
* Scalable architecture for 10,000+ symbols

APIs Integrated:
---------------
* TwelveData API (primary financial data)
* Binance API (cryptocurrency data)
* NewsAPI (financial news)
* Yahoo Finance (additional data)
* Financial Modeling Prep (events)
    """,
    'author': 'TradingView Clone Team',
    'website': 'https://www.tradingview.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'web',
        'website',
        'website_forum',
        'portal',
    ],
    'data': [
        # Security
        'security/security.xml',
        'security/ir.model.access.csv',

        # Data
        'data/ir_cron.xml',

        # Views - Backend
        'views/menu_views.xml',
        'views/symbol_views.xml',
        'views/ohlc_views.xml',
        'views/technical_views.xml',
        'views/news_views.xml',
        'views/event_views.xml',
        'views/watchlist_views.xml',
        'views/sync_log_views.xml',

        # Wizards
        'views/sync_wizard_views.xml',

        # Website Templates
        'views/website_templates.xml',
        'views/symbol_detail_template.xml',
        'views/market_explorer_template.xml',
        'views/portal_templates.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'tradingview_clone/static/src/css/main.css',
            'tradingview_clone/static/src/js/main.js',
            'tradingview_clone/static/src/js/chart_integration.js',
            'tradingview_clone/static/src/js/market_explorer.js',
        ],
    },
    'demo': [
        'demo/demo_symbols.xml',
        'demo/demo_ohlc_data.xml',
    ],
    'images': [
        'static/description/icon.png',
        'static/description/banner.png',
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 10,
    'external_dependencies': {
        'python': [
            'requests',
            'python-dateutil',
        ],
    },
}
