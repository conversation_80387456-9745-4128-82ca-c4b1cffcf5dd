# TradingView Clone - Odoo Module

A comprehensive financial market data platform built as an Odoo 17 module, providing real-time market data, interactive charts, news aggregation, and portfolio management capabilities.

## 🚀 Features

### Core Functionality
- **Multi-Asset Support**: Stocks, Cryptocurrencies, Forex, Commodities, and Indices
- **Real-time Data**: Live price updates and market data synchronization
- **Interactive Charts**: TradingView Lightweight Charts integration with technical indicators
- **News Aggregation**: Financial news from multiple sources with sentiment analysis
- **Event Calendar**: Earnings reports, economic events, and market announcements
- **User Watchlists**: Personal portfolio tracking with performance analytics
- **Price Alerts**: Customizable price notifications and alerts

### Technical Features
- **API Integrations**: TwelveData, Binance, NewsAPI, and more
- **Automated Sync**: Cron jobs for continuous data updates
- **Responsive Design**: Mobile-friendly interface with Bootstrap 5
- **Performance Optimized**: Efficient database queries and caching
- **Scalable Architecture**: Handles 10,000+ symbols efficiently

## 📋 Requirements

### System Requirements
- **Odoo**: Version 17.0 or higher
- **Python**: 3.8+ with required packages
- **Database**: PostgreSQL 12+ (recommended)
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **Storage**: 10GB+ for data storage

### Python Dependencies
```bash
pip install requests python-dateutil
```

### External APIs (Optional but Recommended)
- **TwelveData API**: For stock and forex data
- **Binance API**: For cryptocurrency data  
- **NewsAPI**: For financial news aggregation
- **Financial Modeling Prep**: For earnings and events data

## 🛠️ Installation

### 1. Download and Install Module

```bash
# Clone or download the module to your Odoo addons directory
cd /path/to/odoo/addons
git clone <repository-url> tradingview_clone

# Or extract the module archive
unzip tradingview_clone.zip
```

### 2. Install Dependencies

```bash
# Install Python dependencies
pip install -r tradingview_clone/requirements.txt
```

### 3. Update Odoo Apps List

```bash
# Restart Odoo server
sudo systemctl restart odoo

# Or update apps list from Odoo interface
# Apps → Update Apps List
```

### 4. Install the Module

1. Go to **Apps** in Odoo
2. Search for "TradingView Clone"
3. Click **Install**

## ⚙️ Configuration

### 1. API Keys Setup

Navigate to **Settings → Technical → Parameters → System Parameters** and add:

```
tradingview.twelvedata_api_key = your_twelvedata_api_key
tradingview.newsapi_key = your_newsapi_key
tradingview.binance_api_key = your_binance_api_key (optional)
tradingview.binance_secret_key = your_binance_secret_key (optional)
```

### 2. Initial Data Sync

1. Go to **Market → Sync Wizard**
2. Select data types to synchronize:
   - ✅ Sync Symbols
   - ✅ Sync OHLC Data  
   - ✅ Sync News
   - ✅ Sync Events
3. Choose symbol types (All Types recommended)
4. Click **Start Synchronization**

### 3. Cron Jobs Configuration

The module automatically creates cron jobs for:
- **Daily Symbol Sync**: Updates symbol list
- **5-Minute OHLC Sync**: Real-time price updates (trading hours)
- **Hourly News Sync**: Latest financial news
- **Technical Indicators**: 15-minute calculations
- **Price Alerts**: 5-minute alert checks

## 🎯 Usage Guide

### For End Users

#### Exploring Markets
1. Visit `/market` to browse all available symbols
2. Use filters to narrow down by:
   - Symbol Type (Stocks, Crypto, Forex, etc.)
   - Exchange (NYSE, NASDAQ, Binance, etc.)
   - Region (US, Europe, Asia, etc.)
   - Sector (Technology, Finance, etc.)

#### Symbol Details
1. Click any symbol to view detailed information
2. Interactive chart with multiple timeframes
3. Tabs for Overview, News, Technical Analysis, Events
4. Add to watchlist for tracking

#### Managing Watchlist
1. Go to **My Account → My Watchlist**
2. View portfolio performance and statistics
3. Set price alerts and target prices
4. Organize symbols by categories

### For Administrators

#### Symbol Management
1. **Market → Symbols**: Manage symbol database
2. **Market → OHLC Data**: View price history
3. **Market → Technical Indicators**: Monitor calculations
4. **Market → News**: Manage news articles
5. **Market → Events**: Track financial events

#### Sync Monitoring
1. **Market → Sync Logs**: Monitor data synchronization
2. **Market → Manual Sync**: Trigger manual updates
3. Check sync status and error logs

## 🔧 API Endpoints

### Public API Endpoints

```bash
# Get symbols list
POST /market/api/symbols
{
  "symbol_type": "stock",
  "limit": 50,
  "offset": 0
}

# Get symbol details
POST /market/api/symbol/AAPL

# Get OHLC data
POST /market/api/ohlc/AAPL
{
  "timeframe": "1d",
  "limit": 100
}

# Get news for symbol
POST /market/api/news/AAPL

# Get market status
POST /market/api/market-status
```

### User-Specific Endpoints (Requires Authentication)

```bash
# Get user watchlist
POST /api/v1/watchlist

# Add price alert
POST /api/v1/alerts/price
{
  "symbol": "AAPL",
  "price_above": 200.00,
  "price_below": 150.00
}

# Portfolio performance
POST /api/v1/portfolio/performance
```

## 🎨 Customization

### Adding New Data Sources

1. Create a new method in `models/api_service.py`:

```python
def sync_new_source_data(self, symbols):
    """Sync data from new source"""
    # Implementation here
    pass
```

2. Add to sync wizard in `wizards/sync_wizard.py`
3. Create corresponding cron job in `data/ir_cron.xml`

### Custom Technical Indicators

1. Add calculation method in `models/technical.py`:

```python
def _calculate_custom_indicator(self, symbol_id):
    """Calculate custom technical indicator"""
    # Implementation here
    pass
```

2. Update indicator selection in views

### Styling Customization

1. Modify `static/src/css/main.css` for custom styling
2. Override templates in `views/` directory
3. Add custom JavaScript in `static/src/js/`

## 🧪 Testing

### Manual Testing Checklist

#### Basic Functionality
- [ ] Module installs without errors
- [ ] Initial data sync completes successfully
- [ ] Market explorer page loads with symbols
- [ ] Symbol detail pages display correctly
- [ ] Charts render properly with data

#### User Features
- [ ] User registration and login works
- [ ] Watchlist add/remove functionality
- [ ] Price alerts can be set and triggered
- [ ] Portfolio statistics calculate correctly
- [ ] Search functionality works

#### Admin Features
- [ ] Backend menus accessible
- [ ] Sync wizard functions properly
- [ ] Cron jobs execute without errors
- [ ] Sync logs show proper status

### Automated Testing

Run the included test suite:

```bash
# Run all tests
python -m pytest tradingview_clone/tests/

# Run specific test categories
python -m pytest tradingview_clone/tests/test_models.py
python -m pytest tradingview_clone/tests/test_controllers.py
python -m pytest tradingview_clone/tests/test_api.py
```

## 📊 Performance Optimization

### Database Optimization
- Indexes on frequently queried fields
- Proper foreign key relationships
- Efficient query patterns

### Caching Strategy
- Symbol data caching
- Chart data caching
- News article caching

### Frontend Optimization
- Lazy loading for large datasets
- AJAX for dynamic content
- Optimized image loading

## 🔒 Security Considerations

### API Security
- Rate limiting on public endpoints
- Authentication for user-specific data
- Input validation and sanitization

### Data Protection
- User watchlist privacy
- Secure API key storage
- CSRF protection on forms

## 🐛 Troubleshooting

### Common Issues

#### Sync Failures
```bash
# Check sync logs
Market → Sync Logs → Filter by "failure"

# Verify API keys
Settings → Technical → System Parameters

# Check cron job status
Settings → Technical → Automation → Scheduled Actions
```

#### Chart Not Loading
```bash
# Check browser console for JavaScript errors
# Verify TradingView Lightweight Charts library loading
# Check symbol data availability
```

#### Performance Issues
```bash
# Monitor database queries
# Check server resources (CPU, Memory)
# Review cron job frequency
# Optimize database indexes
```

### Support

For technical support and bug reports:
1. Check the troubleshooting section above
2. Review sync logs for error details
3. Verify API configurations
4. Contact system administrator

## 📈 Roadmap

### Upcoming Features
- [ ] Real-time WebSocket connections
- [ ] Advanced charting tools
- [ ] Social trading features
- [ ] Mobile app integration
- [ ] Advanced portfolio analytics
- [ ] Options and derivatives support

### Performance Improvements
- [ ] Redis caching integration
- [ ] Database query optimization
- [ ] CDN integration for static assets
- [ ] Microservices architecture

## 📄 License

This module is licensed under the LGPL-3 license. See LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please read the contributing guidelines and submit pull requests for any improvements.

---

**Built with ❤️ for the Odoo community**
