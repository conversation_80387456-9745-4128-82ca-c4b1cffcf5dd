# -*- coding: utf-8 -*-

from odoo import http, fields, _
from odoo.http import request
from odoo.exceptions import UserError, AccessError
import json
import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class TradingViewAPIController(http.Controller):
    """API Controller for external integrations and advanced features"""

    def _validate_api_access(self):
        """Validate API access permissions"""
        if request.env.user._is_public():
            raise AccessError(_('API access requires authentication'))
    
    @http.route('/api/v1/symbols/bulk', type='json', auth='user', methods=['POST'])
    def bulk_symbol_operations(self, operation='get', symbol_list=None, **kwargs):
        """Bulk operations on symbols"""
        self._validate_api_access()
        
        if not symbol_list:
            return {'error': 'symbol_list is required'}
        
        symbols = request.env['tradingview.symbol'].search([
            ('symbol', 'in', symbol_list),
            ('active', '=', True)
        ])
        
        if operation == 'get':
            result = []
            for symbol in symbols:
                result.append({
                    'symbol': symbol.symbol,
                    'name': symbol.name,
                    'current_price': symbol.current_price,
                    'daily_change_percent': symbol.daily_change_percent,
                    'volume': symbol.volume,
                    'last_updated': symbol.last_updated.isoformat() if symbol.last_updated else None,
                })
            return {'symbols': result}
        
        elif operation == 'add_to_watchlist':
            category = kwargs.get('category', 'stocks')
            added_count = 0
            errors = []
            
            for symbol in symbols:
                try:
                    request.env['tradingview.watchlist'].add_to_watchlist(
                        symbol.id, request.env.user.id, category
                    )
                    added_count += 1
                except UserError as e:
                    errors.append({'symbol': symbol.symbol, 'error': str(e)})
            
            return {
                'added_count': added_count,
                'errors': errors,
                'total_requested': len(symbol_list)
            }
        
        else:
            return {'error': f'Unknown operation: {operation}'}
    
    @http.route('/api/v1/portfolio/performance', type='json', auth='user')
    def get_portfolio_performance(self, period='1d'):
        """Get portfolio performance for user's watchlist"""
        self._validate_api_access()
        
        watchlist_items = request.env['tradingview.watchlist'].search([
            ('user_id', '=', request.env.user.id),
            ('is_active', '=', True)
        ])
        
        total_value = 0
        total_change = 0
        total_change_percent = 0
        symbols_data = []
        
        for item in watchlist_items:
            symbol = item.symbol_id
            if symbol.current_price:
                # Calculate position value (assuming 1 share for demo)
                position_value = symbol.current_price
                position_change = symbol.daily_change
                position_change_percent = symbol.daily_change_percent
                
                total_value += position_value
                total_change += position_change
                
                symbols_data.append({
                    'symbol': symbol.symbol,
                    'name': symbol.name,
                    'current_price': symbol.current_price,
                    'daily_change': symbol.daily_change,
                    'daily_change_percent': symbol.daily_change_percent,
                    'performance_since_added': item.performance_since_added,
                    'position_value': position_value,
                    'weight': 0  # Will be calculated below
                })
        
        # Calculate weights
        if total_value > 0:
            for symbol_data in symbols_data:
                symbol_data['weight'] = (symbol_data['position_value'] / total_value) * 100
            
            total_change_percent = (total_change / (total_value - total_change)) * 100 if total_value != total_change else 0
        
        return {
            'portfolio': {
                'total_value': total_value,
                'total_change': total_change,
                'total_change_percent': total_change_percent,
                'symbols_count': len(symbols_data),
                'period': period
            },
            'positions': symbols_data
        }
    
    @http.route('/api/v1/alerts/price', type='json', auth='user', methods=['POST'])
    def create_price_alert(self, symbol, price_above=None, price_below=None, **kwargs):
        """Create or update price alert for a symbol"""
        self._validate_api_access()
        
        symbol_obj = request.env['tradingview.symbol'].search([
            ('symbol', '=', symbol.upper())
        ], limit=1)
        
        if not symbol_obj:
            return {'error': 'Symbol not found'}
        
        # Check if symbol is in user's watchlist
        watchlist_item = request.env['tradingview.watchlist'].search([
            ('user_id', '=', request.env.user.id),
            ('symbol_id', '=', symbol_obj.id)
        ], limit=1)
        
        if not watchlist_item:
            # Add to watchlist first
            try:
                request.env['tradingview.watchlist'].add_to_watchlist(
                    symbol_obj.id, request.env.user.id
                )
                watchlist_item = request.env['tradingview.watchlist'].search([
                    ('user_id', '=', request.env.user.id),
                    ('symbol_id', '=', symbol_obj.id)
                ], limit=1)
            except UserError as e:
                return {'error': str(e)}
        
        # Update alert settings
        update_vals = {
            'price_alert_enabled': True
        }
        
        if price_above is not None:
            update_vals['price_alert_above'] = price_above
        
        if price_below is not None:
            update_vals['price_alert_below'] = price_below
        
        watchlist_item.write(update_vals)
        
        return {
            'success': True,
            'message': f'Price alert created for {symbol}',
            'alert_settings': {
                'symbol': symbol,
                'price_above': watchlist_item.price_alert_above,
                'price_below': watchlist_item.price_alert_below,
                'enabled': watchlist_item.price_alert_enabled
            }
        }
    
    @http.route('/api/v1/alerts/list', type='json', auth='user')
    def list_price_alerts(self):
        """List all active price alerts for user"""
        self._validate_api_access()
        
        alerts = request.env['tradingview.watchlist'].search([
            ('user_id', '=', request.env.user.id),
            ('price_alert_enabled', '=', True),
            ('is_active', '=', True)
        ])
        
        alerts_data = []
        for alert in alerts:
            alerts_data.append({
                'symbol': alert.symbol_id.symbol,
                'name': alert.symbol_id.name,
                'current_price': alert.current_price,
                'price_alert_above': alert.price_alert_above,
                'price_alert_below': alert.price_alert_below,
                'added_date': alert.added_date.isoformat(),
                'last_viewed': alert.last_viewed.isoformat() if alert.last_viewed else None,
            })
        
        return {
            'alerts': alerts_data,
            'count': len(alerts_data)
        }
    
    @http.route('/api/v1/news/feed', type='json', auth='public')
    def get_news_feed(self, limit=20, category=None, symbol_type=None):
        """Get personalized news feed"""
        
        domain = [('active', '=', True)]
        
        if category:
            domain.append(('category', '=', category))
        
        if symbol_type:
            domain.append(('symbol_id.type', '=', symbol_type))
        
        # If user is logged in, prioritize news for their watchlist symbols
        if not request.env.user._is_public():
            watchlist_symbols = request.env['tradingview.watchlist'].search([
                ('user_id', '=', request.env.user.id),
                ('is_active', '=', True)
            ]).mapped('symbol_id')
            
            if watchlist_symbols:
                # Get news for watchlist symbols first
                watchlist_news = request.env['tradingview.news'].search(
                    domain + [('symbol_id', 'in', watchlist_symbols.ids)],
                    order='published_at desc',
                    limit=limit // 2
                )
                
                # Get general news
                general_news = request.env['tradingview.news'].search(
                    domain + [('symbol_id', 'not in', watchlist_symbols.ids)],
                    order='published_at desc',
                    limit=limit - len(watchlist_news)
                )
                
                news_records = watchlist_news + general_news
            else:
                news_records = request.env['tradingview.news'].search(
                    domain, order='published_at desc', limit=limit
                )
        else:
            news_records = request.env['tradingview.news'].search(
                domain, order='published_at desc', limit=limit
            )
        
        news_data = []
        for news in news_records:
            news_data.append({
                'title': news.title,
                'summary': news.display_summary,
                'link': news.link,
                'source': news.source,
                'published_at': news.published_at.isoformat(),
                'category': news.category,
                'sentiment': news.sentiment,
                'impact_level': news.impact_level,
                'is_breaking': news.is_breaking,
                'is_featured': news.is_featured,
                'symbol': {
                    'symbol': news.symbol_id.symbol,
                    'name': news.symbol_id.name,
                    'slug': news.symbol_id.slug,
                    'type': news.symbol_id.type
                }
            })
        
        return {
            'news': news_data,
            'count': len(news_data),
            'personalized': not request.env.user._is_public()
        }
    
    @http.route('/api/v1/events/calendar', type='json', auth='public')
    def get_events_calendar(self, start_date=None, end_date=None, event_type=None, impact_level=None):
        """Get events calendar with filtering"""
        
        domain = []
        
        if start_date:
            try:
                start_dt = datetime.fromisoformat(start_date)
                domain.append(('date', '>=', start_dt))
            except ValueError:
                return {'error': 'Invalid start_date format'}
        else:
            # Default to today
            domain.append(('date', '>=', fields.Datetime.now()))
        
        if end_date:
            try:
                end_dt = datetime.fromisoformat(end_date)
                domain.append(('date', '<=', end_dt))
            except ValueError:
                return {'error': 'Invalid end_date format'}
        else:
            # Default to 30 days from now
            domain.append(('date', '<=', fields.Datetime.now() + timedelta(days=30)))
        
        if event_type:
            domain.append(('event_type', '=', event_type))
        
        if impact_level:
            domain.append(('impact_level', '=', impact_level))
        
        events = request.env['tradingview.event'].search(
            domain, order='date asc', limit=100
        )
        
        events_data = []
        for event in events:
            events_data.append({
                'title': event.title,
                'description': event.description,
                'date': event.date.isoformat(),
                'event_type': event.event_type,
                'impact_level': event.impact_level,
                'status': event.status,
                'is_upcoming': event.is_upcoming,
                'time_until': event.time_until,
                'symbol': {
                    'symbol': event.symbol_id.symbol,
                    'name': event.symbol_id.name,
                    'slug': event.symbol_id.slug,
                    'type': event.symbol_id.type
                }
            })
        
        return {
            'events': events_data,
            'count': len(events_data),
            'filters': {
                'start_date': start_date,
                'end_date': end_date,
                'event_type': event_type,
                'impact_level': impact_level
            }
        }
    
    @http.route('/api/v1/sync/status', type='json', auth='user')
    def get_sync_status(self, limit=10):
        """Get recent synchronization status"""
        self._validate_api_access()
        
        # Check if user has manager permissions
        if not request.env.user.has_group('tradingview_clone.group_tradingview_manager'):
            raise AccessError(_('Insufficient permissions to view sync status'))
        
        recent_syncs = request.env['tradingview.sync_log'].search([
            ('start_time', '>=', fields.Datetime.now() - timedelta(hours=24))
        ], order='start_time desc', limit=limit)
        
        sync_data = []
        for sync in recent_syncs:
            sync_data.append({
                'api_name': sync.api_name,
                'sync_type': sync.sync_type,
                'status': sync.status,
                'start_time': sync.start_time.isoformat(),
                'duration_seconds': sync.duration_seconds,
                'records_processed': sync.records_processed,
                'success_rate': sync.success_rate,
                'error_message': sync.error_message,
            })
        
        # Get overall statistics
        stats = request.env['tradingview.sync_log'].get_sync_statistics(days=7)
        
        return {
            'recent_syncs': sync_data,
            'statistics': stats
        }
