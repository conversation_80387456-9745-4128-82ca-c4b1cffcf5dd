# -*- coding: utf-8 -*-

from odoo import http, fields, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from odoo.exceptions import AccessError, UserError
import logging

_logger = logging.getLogger(__name__)


class TradingViewPortal(CustomerPortal):
    
    def _prepare_home_portal_values(self, counters):
        """Add watchlist count to portal home"""
        values = super()._prepare_home_portal_values(counters)
        
        if 'watchlist_count' in counters:
            watchlist_count = request.env['tradingview.watchlist'].search_count([
                ('user_id', '=', request.env.user.id),
                ('is_active', '=', True)
            ]) if request.env.user.id else 0
            values['watchlist_count'] = watchlist_count
        
        return values
    
    @http.route(['/my/watchlist', '/my/watchlist/page/<int:page>'], type='http', auth='user', website=True)
    def portal_my_watchlist(self, page=1, date_begin=None, date_end=None, sortby=None, search=None, search_in='symbol', category=None, **kw):
        """User's watchlist portal page"""
        
        values = self._prepare_portal_layout_values()
        Watchlist = request.env['tradingview.watchlist']
        
        domain = [
            ('user_id', '=', request.env.user.id),
            ('is_active', '=', True)
        ]
        
        searchbar_sortings = {
            'date': {'label': _('Added Date'), 'order': 'added_date desc'},
            'symbol': {'label': _('Symbol'), 'order': 'symbol_id'},
            'performance': {'label': _('Performance'), 'order': 'performance_since_added desc'},
            'price': {'label': _('Current Price'), 'order': 'current_price desc'},
            'change': {'label': _('Daily Change'), 'order': 'daily_change_percent desc'},
        }
        
        searchbar_inputs = {
            'symbol': {'input': 'symbol', 'label': _('Search in Symbol')},
            'name': {'input': 'name', 'label': _('Search in Name')},
            'all': {'input': 'all', 'label': _('Search in All')},
        }
        
        searchbar_filters = {
            'all': {'label': _('All'), 'domain': []},
            'stocks': {'label': _('Stocks'), 'domain': [('category', '=', 'stocks')]},
            'crypto': {'label': _('Cryptocurrency'), 'domain': [('category', '=', 'crypto')]},
            'forex': {'label': _('Forex'), 'domain': [('category', '=', 'forex')]},
            'commodities': {'label': _('Commodities'), 'domain': [('category', '=', 'commodities')]},
            'favorites': {'label': _('Favorites'), 'domain': [('category', '=', 'favorites')]},
        }
        
        # Default sort order
        if not sortby:
            sortby = 'date'
        order = searchbar_sortings[sortby]['order']
        
        # Filter by category
        if category and category in searchbar_filters:
            domain += searchbar_filters[category]['domain']
        
        # Search
        if search and search_in:
            search_domain = []
            if search_in in ('symbol', 'all'):
                search_domain = ['|', ('symbol_id.symbol', 'ilike', search), ('symbol_id.name', 'ilike', search)]
            elif search_in == 'name':
                search_domain = [('symbol_id.name', 'ilike', search)]
            
            domain += search_domain
        
        # Date filtering
        if date_begin and date_end:
            domain += [('added_date', '>', date_begin), ('added_date', '<=', date_end)]
        
        # Count for pager
        watchlist_count = Watchlist.search_count(domain)
        
        # Pager
        pager = portal_pager(
            url="/my/watchlist",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby, 'search_in': search_in, 'search': search, 'category': category},
            total=watchlist_count,
            page=page,
            step=self._items_per_page
        )
        
        # Get watchlist items
        watchlist_items = Watchlist.search(domain, order=order, limit=self._items_per_page, offset=pager['offset'])
        
        # Get categories with counts
        categories = Watchlist.get_watchlist_categories(request.env.user.id)
        
        # Calculate portfolio performance
        portfolio_stats = self._calculate_portfolio_stats(watchlist_items)
        
        values.update({
            'date': date_begin,
            'date_end': date_end,
            'watchlist_items': watchlist_items,
            'page_name': 'watchlist',
            'archive_groups': [],
            'default_url': '/my/watchlist',
            'pager': pager,
            'searchbar_sortings': searchbar_sortings,
            'searchbar_inputs': searchbar_inputs,
            'searchbar_filters': searchbar_filters,
            'search_in': search_in,
            'search': search,
            'sortby': sortby,
            'category': category,
            'categories': categories,
            'portfolio_stats': portfolio_stats,
        })
        
        return request.render("tradingview_clone.portal_my_watchlist", values)
    
    @http.route(['/my/watchlist/<int:watchlist_id>'], type='http', auth='user', website=True)
    def portal_watchlist_detail(self, watchlist_id, **kw):
        """Watchlist item detail page"""
        
        try:
            watchlist_item = self._document_check_access('tradingview.watchlist', watchlist_id)
        except (AccessError, UserError):
            return request.redirect('/my')
        
        # Get related data
        symbol = watchlist_item.symbol_id
        latest_ohlc = symbol.get_latest_ohlc(limit=30)
        latest_news = symbol.get_latest_news(limit=5)
        upcoming_events = symbol.get_upcoming_events(limit=3)
        
        # Get technical indicators
        technical_indicators = request.env['tradingview.technical'].get_latest_indicators(
            symbol.id, timeframe='1d'
        )
        
        values = {
            'watchlist_item': watchlist_item,
            'symbol': symbol,
            'latest_ohlc': latest_ohlc,
            'latest_news': latest_news,
            'upcoming_events': upcoming_events,
            'technical_indicators': technical_indicators,
            'page_name': 'watchlist_detail',
        }
        
        return request.render("tradingview_clone.portal_watchlist_detail", values)
    
    @http.route(['/my/watchlist/<int:watchlist_id>/edit'], type='http', auth='user', website=True)
    def portal_watchlist_edit(self, watchlist_id, **kw):
        """Edit watchlist item"""
        
        try:
            watchlist_item = self._document_check_access('tradingview.watchlist', watchlist_id)
        except (AccessError, UserError):
            return request.redirect('/my')
        
        if request.httprequest.method == 'POST':
            # Update watchlist item
            update_vals = {}
            
            if 'target_price' in kw:
                try:
                    update_vals['target_price'] = float(kw['target_price']) if kw['target_price'] else 0
                except ValueError:
                    pass
            
            if 'stop_loss' in kw:
                try:
                    update_vals['stop_loss'] = float(kw['stop_loss']) if kw['stop_loss'] else 0
                except ValueError:
                    pass
            
            if 'notes' in kw:
                update_vals['notes'] = kw['notes']
            
            if 'category' in kw:
                update_vals['category'] = kw['category']
            
            if 'custom_category' in kw and kw['category'] == 'custom':
                update_vals['custom_category'] = kw['custom_category']
            
            # Price alerts
            if 'price_alert_enabled' in kw:
                update_vals['price_alert_enabled'] = True
                if 'price_alert_above' in kw:
                    try:
                        update_vals['price_alert_above'] = float(kw['price_alert_above']) if kw['price_alert_above'] else 0
                    except ValueError:
                        pass
                if 'price_alert_below' in kw:
                    try:
                        update_vals['price_alert_below'] = float(kw['price_alert_below']) if kw['price_alert_below'] else 0
                    except ValueError:
                        pass
            else:
                update_vals['price_alert_enabled'] = False
            
            if update_vals:
                watchlist_item.write(update_vals)
            
            return request.redirect(f'/my/watchlist/{watchlist_id}')
        
        values = {
            'watchlist_item': watchlist_item,
            'page_name': 'watchlist_edit',
        }
        
        return request.render("tradingview_clone.portal_watchlist_edit", values)
    
    @http.route(['/my/watchlist/<int:watchlist_id>/remove'], type='http', auth='user', website=True, methods=['POST'])
    def portal_watchlist_remove(self, watchlist_id, **kw):
        """Remove item from watchlist"""
        
        try:
            watchlist_item = self._document_check_access('tradingview.watchlist', watchlist_id)
            watchlist_item.unlink()
        except (AccessError, UserError):
            pass
        
        return request.redirect('/my/watchlist')
    
    @http.route(['/my/alerts'], type='http', auth='user', website=True)
    def portal_my_alerts(self, **kw):
        """User's price alerts page"""
        
        values = self._prepare_portal_layout_values()
        
        # Get active alerts
        alerts = request.env['tradingview.watchlist'].search([
            ('user_id', '=', request.env.user.id),
            ('price_alert_enabled', '=', True),
            ('is_active', '=', True)
        ], order='added_date desc')
        
        values.update({
            'alerts': alerts,
            'page_name': 'alerts',
        })
        
        return request.render("tradingview_clone.portal_my_alerts", values)
    
    @http.route(['/my/portfolio'], type='http', auth='user', website=True)
    def portal_my_portfolio(self, **kw):
        """User's portfolio overview"""
        
        values = self._prepare_portal_layout_values()
        
        # Get all watchlist items
        watchlist_items = request.env['tradingview.watchlist'].search([
            ('user_id', '=', request.env.user.id),
            ('is_active', '=', True)
        ])
        
        # Calculate portfolio statistics
        portfolio_stats = self._calculate_portfolio_stats(watchlist_items)
        
        # Get performance data by category
        category_performance = self._get_category_performance(watchlist_items)
        
        # Get top performers
        top_performers = watchlist_items.sorted('performance_since_added', reverse=True)[:5]
        worst_performers = watchlist_items.sorted('performance_since_added')[:5]
        
        values.update({
            'watchlist_items': watchlist_items,
            'portfolio_stats': portfolio_stats,
            'category_performance': category_performance,
            'top_performers': top_performers,
            'worst_performers': worst_performers,
            'page_name': 'portfolio',
        })
        
        return request.render("tradingview_clone.portal_my_portfolio", values)
    
    def _calculate_portfolio_stats(self, watchlist_items):
        """Calculate portfolio statistics"""
        if not watchlist_items:
            return {
                'total_symbols': 0,
                'total_value': 0,
                'total_change': 0,
                'total_change_percent': 0,
                'positive_count': 0,
                'negative_count': 0,
            }
        
        total_value = sum(item.current_price or 0 for item in watchlist_items)
        total_change = sum(item.daily_change or 0 for item in watchlist_items)
        
        positive_count = len([item for item in watchlist_items if (item.daily_change_percent or 0) > 0])
        negative_count = len([item for item in watchlist_items if (item.daily_change_percent or 0) < 0])
        
        total_change_percent = (total_change / (total_value - total_change)) * 100 if total_value != total_change else 0
        
        return {
            'total_symbols': len(watchlist_items),
            'total_value': total_value,
            'total_change': total_change,
            'total_change_percent': total_change_percent,
            'positive_count': positive_count,
            'negative_count': negative_count,
        }
    
    def _get_category_performance(self, watchlist_items):
        """Get performance by category"""
        categories = {}
        
        for item in watchlist_items:
            category = item.category or 'other'
            if category not in categories:
                categories[category] = {
                    'count': 0,
                    'total_change': 0,
                    'avg_change': 0,
                }
            
            categories[category]['count'] += 1
            categories[category]['total_change'] += item.daily_change_percent or 0
        
        # Calculate averages
        for category_data in categories.values():
            if category_data['count'] > 0:
                category_data['avg_change'] = category_data['total_change'] / category_data['count']
        
        return categories
    
    def _document_check_access(self, model_name, document_id, access_token=None):
        """Check access to document"""
        document = request.env[model_name].browse([document_id])
        document_sudo = document.sudo()
        
        try:
            document.check_access_rights('read')
            document.check_access_rule('read')
        except AccessError:
            raise
        
        # Check if user owns the watchlist item
        if model_name == 'tradingview.watchlist' and document_sudo.user_id.id != request.env.user.id:
            raise AccessError(_("You don't have access to this watchlist item."))
        
        return document_sudo
