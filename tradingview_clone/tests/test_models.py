# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class TestTradingViewModels(TransactionCase):
    
    def setUp(self):
        super().setUp()
        
        # Create test symbol
        self.test_symbol = self.env['tradingview.symbol'].create({
            'symbol': 'TESTBTC',
            'name': 'Test Bitcoin',
            'type': 'crypto',
            'exchange': 'TEST',
            'region': 'Global',
            'currency': 'USD',
            'active': True,
        })
        
        # Create test user
        self.test_user = self.env['res.users'].create({
            'name': 'Test User',
            'login': '<EMAIL>',
            'email': '<EMAIL>',
        })
    
    def test_symbol_creation(self):
        """Test symbol model creation and validation"""
        symbol = self.env['tradingview.symbol'].create({
            'symbol': 'AAPL',
            'name': 'Apple Inc.',
            'type': 'stock',
            'exchange': 'NASDAQ',
            'region': 'US',
            'currency': 'USD',
            'sector': 'Technology',
            'industry': 'Consumer Electronics',
        })
        
        self.assertEqual(symbol.symbol, 'AAPL')
        self.assertEqual(symbol.type, 'stock')
        self.assertTrue(symbol.active)
        self.assertIsNotNone(symbol.slug)
    
    def test_symbol_slug_generation(self):
        """Test automatic slug generation"""
        symbol = self.env['tradingview.symbol'].create({
            'symbol': 'BTC-USD',
            'name': 'Bitcoin USD',
            'type': 'crypto',
            'exchange': 'CRYPTO',
        })
        
        self.assertEqual(symbol.slug, 'btc-usd')
    
    def test_symbol_duplicate_prevention(self):
        """Test that duplicate symbols are prevented"""
        with self.assertRaises(ValidationError):
            self.env['tradingview.symbol'].create({
                'symbol': 'TESTBTC',  # Same as test_symbol
                'name': 'Duplicate Test',
                'type': 'crypto',
                'exchange': 'TEST',
            })
    
    def test_ohlc_creation(self):
        """Test OHLC data creation and computed fields"""
        ohlc = self.env['tradingview.ohlc'].create({
            'symbol_id': self.test_symbol.id,
            'timestamp': datetime.now(),
            'timeframe': '1d',
            'open': 50000.0,
            'high': 52000.0,
            'low': 49000.0,
            'close': 51000.0,
            'volume': 1000000,
        })
        
        self.assertEqual(ohlc.price_change, 1000.0)  # close - open
        self.assertEqual(ohlc.price_change_percent, 2.0)  # (1000/50000)*100
        self.assertEqual(ohlc.price_range, 3000.0)  # high - low
        self.assertTrue(ohlc.is_bullish)  # close > open
    
    def test_ohlc_validation(self):
        """Test OHLC data validation"""
        with self.assertRaises(ValidationError):
            self.env['tradingview.ohlc'].create({
                'symbol_id': self.test_symbol.id,
                'timestamp': datetime.now(),
                'timeframe': '1d',
                'open': 50000.0,
                'high': 48000.0,  # High < Open (invalid)
                'low': 49000.0,
                'close': 51000.0,
                'volume': 1000000,
            })
    
    def test_watchlist_creation(self):
        """Test watchlist functionality"""
        watchlist = self.env['tradingview.watchlist'].create({
            'user_id': self.test_user.id,
            'symbol_id': self.test_symbol.id,
            'category': 'crypto',
            'price_when_added': 50000.0,
        })
        
        self.assertEqual(watchlist.user_id.id, self.test_user.id)
        self.assertEqual(watchlist.symbol_id.id, self.test_symbol.id)
        self.assertTrue(watchlist.is_active)
    
    def test_watchlist_performance_calculation(self):
        """Test watchlist performance calculation"""
        # Create OHLC data for current price
        self.env['tradingview.ohlc'].create({
            'symbol_id': self.test_symbol.id,
            'timestamp': datetime.now(),
            'timeframe': '1d',
            'open': 50000.0,
            'high': 52000.0,
            'low': 49000.0,
            'close': 55000.0,  # Current price
            'volume': 1000000,
        })
        
        # Update symbol current price
        self.test_symbol.current_price = 55000.0
        
        watchlist = self.env['tradingview.watchlist'].create({
            'user_id': self.test_user.id,
            'symbol_id': self.test_symbol.id,
            'category': 'crypto',
            'price_when_added': 50000.0,
        })
        
        # Performance should be 10% ((55000-50000)/50000*100)
        self.assertEqual(watchlist.performance_since_added, 10.0)
    
    def test_news_creation(self):
        """Test news model creation"""
        news = self.env['tradingview.news'].create({
            'symbol_id': self.test_symbol.id,
            'title': 'Test Bitcoin News',
            'summary': 'This is a test news article about Bitcoin.',
            'link': 'https://example.com/news/1',
            'source': 'Test News',
            'published_at': datetime.now(),
            'category': 'crypto',
            'sentiment': 'positive',
        })
        
        self.assertEqual(news.title, 'Test Bitcoin News')
        self.assertEqual(news.sentiment, 'positive')
        self.assertTrue(news.active)
        self.assertTrue(news.is_recent)
    
    def test_technical_indicator_creation(self):
        """Test technical indicator creation"""
        technical = self.env['tradingview.technical'].create({
            'symbol_id': self.test_symbol.id,
            'indicator': 'rsi',
            'value': 65.5,
            'timestamp': datetime.now(),
            'timeframe': '1d',
            'signal': 'buy',
            'signal_strength': 75.0,
        })
        
        self.assertEqual(technical.indicator, 'rsi')
        self.assertEqual(technical.signal, 'buy')
        self.assertEqual(technical.signal_strength, 75.0)
    
    def test_event_creation(self):
        """Test event model creation"""
        future_date = datetime.now() + timedelta(days=7)
        
        event = self.env['tradingview.event'].create({
            'symbol_id': self.test_symbol.id,
            'title': 'Test Bitcoin Conference',
            'description': 'Annual Bitcoin conference',
            'date': future_date,
            'event_type': 'conference',
            'impact_level': 'medium',
        })
        
        self.assertEqual(event.title, 'Test Bitcoin Conference')
        self.assertTrue(event.is_upcoming)
        self.assertGreater(event.days_until, 0)
    
    def test_sync_log_creation(self):
        """Test sync log functionality"""
        sync_log = self.env['tradingview.sync_log'].create({
            'api_name': 'test_api',
            'sync_type': 'symbols',
            'status': 'running',
            'start_time': datetime.now(),
            'triggered_by': 'manual',
        })
        
        # Test completion
        sync_log.mark_completed('success')
        self.assertEqual(sync_log.status, 'success')
        self.assertIsNotNone(sync_log.end_time)
        self.assertGreater(sync_log.duration_seconds, 0)
        
        # Test failure
        sync_log2 = self.env['tradingview.sync_log'].create({
            'api_name': 'test_api',
            'sync_type': 'ohlc',
            'status': 'running',
            'start_time': datetime.now(),
            'triggered_by': 'cron',
        })
        
        sync_log2.mark_failed('Test error message')
        self.assertEqual(sync_log2.status, 'failure')
        self.assertEqual(sync_log2.error_message, 'Test error message')
    
    def test_symbol_computed_fields(self):
        """Test symbol computed fields"""
        # Create OHLC data
        self.env['tradingview.ohlc'].create({
            'symbol_id': self.test_symbol.id,
            'timestamp': datetime.now(),
            'timeframe': '1d',
            'open': 50000.0,
            'high': 52000.0,
            'low': 49000.0,
            'close': 51000.0,
            'volume': 1000000,
        })
        
        # Create news
        self.env['tradingview.news'].create({
            'symbol_id': self.test_symbol.id,
            'title': 'Test News',
            'summary': 'Test summary',
            'link': 'https://example.com',
            'source': 'Test',
            'published_at': datetime.now(),
        })
        
        # Create watchlist entry
        self.env['tradingview.watchlist'].create({
            'user_id': self.test_user.id,
            'symbol_id': self.test_symbol.id,
            'category': 'crypto',
        })
        
        # Refresh computed fields
        self.test_symbol._compute_ohlc_count()
        self.test_symbol._compute_news_count()
        self.test_symbol._compute_watchlist_count()
        
        self.assertEqual(self.test_symbol.ohlc_count, 1)
        self.assertEqual(self.test_symbol.news_count, 1)
        self.assertEqual(self.test_symbol.watchlist_count, 1)
    
    def test_bulk_operations(self):
        """Test bulk operations for performance"""
        # Test bulk OHLC creation
        ohlc_data = []
        for i in range(100):
            ohlc_data.append({
                'symbol_id': self.test_symbol.id,
                'timestamp': datetime.now() - timedelta(days=i),
                'timeframe': '1d',
                'open': 50000.0 + i,
                'high': 52000.0 + i,
                'low': 49000.0 + i,
                'close': 51000.0 + i,
                'volume': 1000000,
            })
        
        result = self.env['tradingview.ohlc'].bulk_create_ohlc(ohlc_data)
        self.assertEqual(result['success_count'], 100)
        self.assertEqual(result['error_count'], 0)
    
    def tearDown(self):
        """Clean up test data"""
        super().tearDown()
