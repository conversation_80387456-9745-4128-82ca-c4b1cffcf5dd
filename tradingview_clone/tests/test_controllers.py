# -*- coding: utf-8 -*-

from odoo.tests.common import HttpCase
from odoo.tests import tagged
import json


@tagged('post_install', '-at_install')
class TestTradingViewControllers(HttpCase):
    
    def setUp(self):
        super().setUp()
        
        # Create test data
        self.test_symbol = self.env['tradingview.symbol'].create({
            'symbol': 'TESTBTC',
            'name': 'Test Bitcoin',
            'type': 'crypto',
            'exchange': 'TEST',
            'region': 'Global',
            'currency': 'USD',
            'slug': 'testbtc',
            'active': True,
            'current_price': 50000.0,
            'daily_change': 1000.0,
            'daily_change_percent': 2.0,
        })
        
        # Create test user
        self.test_user = self.env['res.users'].create({
            'name': 'Test User',
            'login': '<EMAIL>',
            'email': '<EMAIL>',
        })
    
    def test_market_explorer_page(self):
        """Test market explorer page loads correctly"""
        response = self.url_open('/market')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Market Explorer', response.content)
        self.assertIn(b'TESTBTC', response.content)
    
    def test_market_explorer_with_filters(self):
        """Test market explorer with filters"""
        response = self.url_open('/market?symbol_type=crypto&search=bitcoin')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'TESTBTC', response.content)
    
    def test_symbol_detail_page(self):
        """Test symbol detail page"""
        response = self.url_open('/market/testbtc')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'TESTBTC', response.content)
        self.assertIn(b'Test Bitcoin', response.content)
        self.assertIn(b'Price Chart', response.content)
    
    def test_symbol_not_found(self):
        """Test symbol not found page"""
        response = self.url_open('/market/nonexistent')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Symbol Not Found', response.content)
    
    def test_symbol_search_api(self):
        """Test symbol search API endpoint"""
        response = self.url_open('/market/search', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {
                'query': 'bitcoin',
                'limit': 10
            }
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('result', data)
        self.assertIn('symbols', data['result'])
    
    def test_chart_data_api(self):
        """Test chart data API endpoint"""
        # Create OHLC data
        self.env['tradingview.ohlc'].create({
            'symbol_id': self.test_symbol.id,
            'timestamp': '2024-01-01 00:00:00',
            'timeframe': '1d',
            'open': 50000.0,
            'high': 52000.0,
            'low': 49000.0,
            'close': 51000.0,
            'volume': 1000000,
        })
        
        response = self.url_open('/market/testbtc/chart-data', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {
                'timeframe': '1d',
                'limit': 100
            }
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('result', data)
        self.assertIn('data', data['result'])
    
    def test_market_api_symbols(self):
        """Test market API symbols endpoint"""
        response = self.url_open('/market/api/symbols', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {
                'symbol_type': 'crypto',
                'limit': 50
            }
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('result', data)
        self.assertIn('symbols', data['result'])
        self.assertGreater(len(data['result']['symbols']), 0)
    
    def test_market_api_symbol_detail(self):
        """Test market API symbol detail endpoint"""
        response = self.url_open('/market/api/symbol/testbtc', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {}
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('result', data)
        self.assertIn('symbol', data['result'])
        self.assertEqual(data['result']['symbol']['symbol'], 'TESTBTC')
    
    def test_market_status_api(self):
        """Test market status API endpoint"""
        response = self.url_open('/market/api/market-status', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {}
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('result', data)
        self.assertIn('market_status', data['result'])
    
    def test_trending_symbols_api(self):
        """Test trending symbols API endpoint"""
        # Add to watchlist to make it trending
        self.env['tradingview.watchlist'].create({
            'user_id': self.test_user.id,
            'symbol_id': self.test_symbol.id,
            'category': 'crypto',
        })
        
        # Update watchlist count
        self.test_symbol.watchlist_count = 1
        
        response = self.url_open('/market/api/trending', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {
                'limit': 10
            }
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('result', data)
        self.assertIn('trending_symbols', data['result'])
    
    def test_watchlist_add_remove(self):
        """Test watchlist add/remove functionality"""
        # Test add to watchlist (requires authentication)
        self.authenticate('<EMAIL>', '<EMAIL>')
        
        response = self.url_open('/market/testbtc/add-to-watchlist', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {
                'category': 'crypto'
            }
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('result', data)
        
        # Verify watchlist item was created
        watchlist_item = self.env['tradingview.watchlist'].search([
            ('user_id', '=', self.test_user.id),
            ('symbol_id', '=', self.test_symbol.id)
        ])
        self.assertTrue(watchlist_item)
        
        # Test remove from watchlist
        response = self.url_open('/market/testbtc/remove-from-watchlist', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {}
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
    
    def test_news_api(self):
        """Test news API endpoint"""
        # Create test news
        self.env['tradingview.news'].create({
            'symbol_id': self.test_symbol.id,
            'title': 'Test Bitcoin News',
            'summary': 'Test news summary',
            'link': 'https://example.com/news',
            'source': 'Test News',
            'published_at': '2024-01-01 12:00:00',
            'category': 'crypto',
        })
        
        response = self.url_open('/market/api/news/testbtc', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {
                'limit': 10
            }
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('result', data)
        self.assertIn('news', data['result'])
        self.assertGreater(len(data['result']['news']), 0)
    
    def test_events_api(self):
        """Test events API endpoint"""
        # Create test event
        self.env['tradingview.event'].create({
            'symbol_id': self.test_symbol.id,
            'title': 'Test Bitcoin Event',
            'description': 'Test event description',
            'date': '2024-12-31 12:00:00',
            'event_type': 'conference',
            'impact_level': 'medium',
        })
        
        response = self.url_open('/market/api/events/testbtc', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {
                'limit': 10,
                'upcoming_only': False
            }
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('result', data)
        self.assertIn('events', data['result'])
    
    def test_technical_indicators_api(self):
        """Test technical indicators API endpoint"""
        # Create test technical indicator
        self.env['tradingview.technical'].create({
            'symbol_id': self.test_symbol.id,
            'indicator': 'rsi',
            'value': 65.5,
            'timestamp': '2024-01-01 12:00:00',
            'timeframe': '1d',
            'signal': 'buy',
            'signal_strength': 75.0,
        })
        
        response = self.url_open('/market/api/technical/testbtc', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {
                'timeframe': '1d'
            }
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('result', data)
        self.assertIn('indicators', data['result'])
    
    def test_pagination(self):
        """Test pagination in market explorer"""
        # Create multiple symbols for pagination testing
        for i in range(60):  # More than default page size
            self.env['tradingview.symbol'].create({
                'symbol': f'TEST{i:03d}',
                'name': f'Test Symbol {i}',
                'type': 'stock',
                'exchange': 'TEST',
                'active': True,
            })
        
        # Test first page
        response = self.url_open('/market')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'TEST000', response.content)
        
        # Test second page
        response = self.url_open('/market/page/2')
        self.assertEqual(response.status_code, 200)
        # Should contain symbols from second page
    
    def test_error_handling(self):
        """Test error handling in controllers"""
        # Test invalid symbol
        response = self.url_open('/market/api/symbol/invalid', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {}
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertIn('result', data)
        self.assertIn('error', data['result'])
    
    def test_performance_large_dataset(self):
        """Test performance with large dataset"""
        # Create many symbols
        symbols_data = []
        for i in range(1000):
            symbols_data.append({
                'symbol': f'PERF{i:04d}',
                'name': f'Performance Test {i}',
                'type': 'stock',
                'exchange': 'TEST',
                'active': True,
            })
        
        # Bulk create for performance
        self.env['tradingview.symbol'].create(symbols_data)
        
        # Test market explorer performance
        response = self.url_open('/market')
        self.assertEqual(response.status_code, 200)
        
        # Test API performance
        response = self.url_open('/market/api/symbols', data=json.dumps({
            'jsonrpc': '2.0',
            'method': 'call',
            'params': {
                'limit': 100
            }
        }), headers={'Content-Type': 'application/json'})
        
        self.assertEqual(response.status_code, 200)
