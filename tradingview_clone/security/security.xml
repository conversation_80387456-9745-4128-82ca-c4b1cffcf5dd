<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Security Groups -->
        <record id="group_tradingview_user" model="res.groups">
            <field name="name">TradingView User</field>
            <field name="category_id" ref="base.module_category_finance"/>
            <field name="comment">Basic user access to TradingView features</field>
        </record>
        
        <record id="group_tradingview_manager" model="res.groups">
            <field name="name">TradingView Manager</field>
            <field name="category_id" ref="base.module_category_finance"/>
            <field name="implied_ids" eval="[(4, ref('group_tradingview_user'))]"/>
            <field name="comment">Full access to TradingView management features</field>
        </record>
        
        <!-- Record Rules -->
        <record id="rule_watchlist_user" model="ir.rule">
            <field name="name">Watchlist: User can only access own watchlist</field>
            <field name="model_id" ref="model_tradingview_watchlist"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_tradingview_user'))]"/>
        </record>
        
        <record id="rule_watchlist_manager" model="ir.rule">
            <field name="name">Watchlist: Manager can access all watchlists</field>
            <field name="model_id" ref="model_tradingview_watchlist"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_tradingview_manager'))]"/>
        </record>
        
    </data>
</odoo>
