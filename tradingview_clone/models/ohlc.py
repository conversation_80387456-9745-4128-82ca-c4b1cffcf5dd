# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class TradingViewOHLC(models.Model):
    _name = 'tradingview.ohlc'
    _description = 'OHLC Price Data'
    _order = 'timestamp desc'
    _rec_name = 'display_name'
    _sql_constraints = [
        ('symbol_timestamp_unique', 'UNIQUE(symbol_id, timestamp, timeframe)', 
         'OHLC data must be unique per symbol, timestamp, and timeframe!'),
        ('price_positive', 'CHECK(open > 0 AND high > 0 AND low > 0 AND close > 0)', 
         'All prices must be positive!'),
        ('high_low_check', 'CHECK(high >= low)', 'High price must be greater than or equal to low price!'),
        ('ohlc_logic_check', 'CHECK(high >= open AND high >= close AND low <= open AND low <= close)', 
         'OHLC prices must follow logical constraints!'),
    ]

    # Core OHLC Data
    symbol_id = fields.Many2one(
        'tradingview.symbol',
        string='Symbol',
        required=True,
        index=True,
        ondelete='cascade'
    )
    timestamp = fields.Datetime(
        string='Timestamp',
        required=True,
        index=True,
        help='Timestamp for this OHLC data point'
    )
    timeframe = fields.Selection([
        ('1m', '1 Minute'),
        ('5m', '5 Minutes'),
        ('15m', '15 Minutes'),
        ('30m', '30 Minutes'),
        ('1h', '1 Hour'),
        ('4h', '4 Hours'),
        ('1d', '1 Day'),
        ('1w', '1 Week'),
        ('1M', '1 Month'),
    ], string='Timeframe', required=True, default='1d')
    
    # OHLC Prices
    open = fields.Float(
        string='Open',
        required=True,
        digits=(16, 6),
        help='Opening price'
    )
    high = fields.Float(
        string='High',
        required=True,
        digits=(16, 6),
        help='Highest price'
    )
    low = fields.Float(
        string='Low',
        required=True,
        digits=(16, 6),
        help='Lowest price'
    )
    close = fields.Float(
        string='Close',
        required=True,
        digits=(16, 6),
        help='Closing price'
    )
    
    # Volume and Additional Data
    volume = fields.Float(
        string='Volume',
        digits=(16, 2),
        help='Trading volume'
    )
    volume_weighted_price = fields.Float(
        string='VWAP',
        digits=(16, 6),
        help='Volume Weighted Average Price'
    )
    trades_count = fields.Integer(
        string='Trades Count',
        help='Number of trades in this period'
    )
    
    # Computed Fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    price_change = fields.Float(
        string='Price Change',
        compute='_compute_price_metrics',
        store=True,
        digits=(16, 6),
        help='Close - Open'
    )
    price_change_percent = fields.Float(
        string='Price Change %',
        compute='_compute_price_metrics',
        store=True,
        digits=(16, 4),
        help='(Close - Open) / Open * 100'
    )
    price_range = fields.Float(
        string='Price Range',
        compute='_compute_price_metrics',
        store=True,
        digits=(16, 6),
        help='High - Low'
    )
    is_bullish = fields.Boolean(
        string='Bullish',
        compute='_compute_price_metrics',
        store=True,
        help='True if close > open'
    )
    
    # Metadata
    created_date = fields.Datetime(string='Created Date', default=fields.Datetime.now)
    data_source = fields.Char(string='Data Source', help='API source of this data')
    
    @api.depends('symbol_id', 'timestamp', 'timeframe')
    def _compute_display_name(self):
        for record in self:
            if record.symbol_id and record.timestamp:
                record.display_name = f"{record.symbol_id.symbol} - {record.timestamp} ({record.timeframe})"
            else:
                record.display_name = 'OHLC Data'
    
    @api.depends('open', 'high', 'low', 'close')
    def _compute_price_metrics(self):
        for record in self:
            if record.open and record.close:
                record.price_change = record.close - record.open
                record.price_change_percent = (record.price_change / record.open) * 100 if record.open else 0
                record.is_bullish = record.close > record.open
            else:
                record.price_change = 0
                record.price_change_percent = 0
                record.is_bullish = False
            
            if record.high and record.low:
                record.price_range = record.high - record.low
            else:
                record.price_range = 0
    
    @api.constrains('open', 'high', 'low', 'close')
    def _check_ohlc_logic(self):
        for record in self:
            if record.open and record.high and record.low and record.close:
                if not (record.low <= record.open <= record.high and 
                       record.low <= record.close <= record.high):
                    raise ValidationError(_(
                        'OHLC data is inconsistent: Open and Close must be between High and Low prices.'
                    ))
    
    @api.model
    def get_chart_data(self, symbol_id, timeframe='1d', limit=100):
        """Get chart data for a symbol in the format expected by charting libraries"""
        domain = [
            ('symbol_id', '=', symbol_id),
            ('timeframe', '=', timeframe)
        ]
        
        records = self.search(domain, order='timestamp asc', limit=limit)
        
        chart_data = []
        for record in records:
            chart_data.append({
                'time': record.timestamp.timestamp() if record.timestamp else 0,
                'open': record.open,
                'high': record.high,
                'low': record.low,
                'close': record.close,
                'volume': record.volume or 0,
            })
        
        return chart_data
    
    @api.model
    def get_latest_price(self, symbol_id, timeframe='1d'):
        """Get the latest price data for a symbol"""
        latest = self.search([
            ('symbol_id', '=', symbol_id),
            ('timeframe', '=', timeframe)
        ], order='timestamp desc', limit=1)
        
        if latest:
            return {
                'price': latest.close,
                'change': latest.price_change,
                'change_percent': latest.price_change_percent,
                'volume': latest.volume,
                'high': latest.high,
                'low': latest.low,
                'timestamp': latest.timestamp,
            }
        return {}
    
    @api.model
    def bulk_create_ohlc(self, ohlc_data_list):
        """Bulk create OHLC records with validation"""
        created_records = []
        errors = []
        
        for data in ohlc_data_list:
            try:
                # Check if record already exists
                existing = self.search([
                    ('symbol_id', '=', data.get('symbol_id')),
                    ('timestamp', '=', data.get('timestamp')),
                    ('timeframe', '=', data.get('timeframe', '1d'))
                ], limit=1)
                
                if not existing:
                    record = self.create(data)
                    created_records.append(record)
                else:
                    # Update existing record
                    existing.write({
                        'open': data.get('open'),
                        'high': data.get('high'),
                        'low': data.get('low'),
                        'close': data.get('close'),
                        'volume': data.get('volume'),
                        'data_source': data.get('data_source'),
                    })
                    created_records.append(existing)
                    
            except Exception as e:
                errors.append({
                    'data': data,
                    'error': str(e)
                })
                _logger.error(f"Error creating OHLC record: {e}")
        
        return {
            'created': created_records,
            'errors': errors,
            'success_count': len(created_records),
            'error_count': len(errors)
        }
    
    def get_technical_levels(self):
        """Calculate basic technical levels for this OHLC data"""
        self.ensure_one()
        
        # Simple support and resistance levels
        pivot = (self.high + self.low + self.close) / 3
        
        return {
            'pivot': pivot,
            'resistance_1': 2 * pivot - self.low,
            'support_1': 2 * pivot - self.high,
            'resistance_2': pivot + (self.high - self.low),
            'support_2': pivot - (self.high - self.low),
        }
    
    @api.model
    def cleanup_old_data(self, days_to_keep=365):
        """Clean up old OHLC data to maintain performance"""
        cutoff_date = fields.Datetime.now() - timedelta(days=days_to_keep)
        
        old_records = self.search([
            ('timestamp', '<', cutoff_date),
            ('timeframe', 'in', ['1m', '5m', '15m', '30m'])  # Only clean up short timeframes
        ])
        
        count = len(old_records)
        old_records.unlink()
        
        _logger.info(f"Cleaned up {count} old OHLC records older than {days_to_keep} days")
        return count
