# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class TradingViewTechnical(models.Model):
    _name = 'tradingview.technical'
    _description = 'Technical Indicators'
    _order = 'timestamp desc, indicator asc'
    _rec_name = 'display_name'
    _sql_constraints = [
        ('symbol_indicator_timestamp_unique', 
         'UNIQUE(symbol_id, indicator, timestamp, timeframe)', 
         'Technical indicator must be unique per symbol, indicator, timestamp, and timeframe!'),
    ]

    # Core Fields
    symbol_id = fields.Many2one(
        'tradingview.symbol',
        string='Symbol',
        required=True,
        index=True,
        ondelete='cascade'
    )
    indicator = fields.Selection([
        # Moving Averages
        ('sma_20', 'SMA 20'),
        ('sma_50', 'SMA 50'),
        ('sma_100', 'SMA 100'),
        ('sma_200', 'SMA 200'),
        ('ema_12', 'EMA 12'),
        ('ema_26', 'EMA 26'),
        ('ema_50', 'EMA 50'),
        ('ema_200', 'EMA 200'),
        
        # Oscillators
        ('rsi', 'RSI'),
        ('rsi_14', 'RSI 14'),
        ('stoch_k', 'Stochastic %K'),
        ('stoch_d', 'Stochastic %D'),
        ('williams_r', 'Williams %R'),
        ('cci', 'Commodity Channel Index'),
        
        # MACD
        ('macd_line', 'MACD Line'),
        ('macd_signal', 'MACD Signal'),
        ('macd_histogram', 'MACD Histogram'),
        
        # Bollinger Bands
        ('bb_upper', 'Bollinger Upper'),
        ('bb_middle', 'Bollinger Middle'),
        ('bb_lower', 'Bollinger Lower'),
        ('bb_width', 'Bollinger Width'),
        
        # Volume Indicators
        ('volume_sma', 'Volume SMA'),
        ('obv', 'On Balance Volume'),
        ('ad_line', 'Accumulation/Distribution'),
        ('mfi', 'Money Flow Index'),
        
        # Trend Indicators
        ('adx', 'Average Directional Index'),
        ('aroon_up', 'Aroon Up'),
        ('aroon_down', 'Aroon Down'),
        ('parabolic_sar', 'Parabolic SAR'),
        
        # Support/Resistance
        ('pivot_point', 'Pivot Point'),
        ('resistance_1', 'Resistance 1'),
        ('resistance_2', 'Resistance 2'),
        ('support_1', 'Support 1'),
        ('support_2', 'Support 2'),
        
        # Custom Indicators
        ('custom', 'Custom Indicator'),
    ], string='Indicator', required=True)
    
    value = fields.Float(
        string='Value',
        required=True,
        digits=(16, 6),
        help='Calculated indicator value'
    )
    
    timestamp = fields.Datetime(
        string='Timestamp',
        required=True,
        index=True,
        help='Timestamp for this indicator calculation'
    )
    
    timeframe = fields.Selection([
        ('1m', '1 Minute'),
        ('5m', '5 Minutes'),
        ('15m', '15 Minutes'),
        ('30m', '30 Minutes'),
        ('1h', '1 Hour'),
        ('4h', '4 Hours'),
        ('1d', '1 Day'),
        ('1w', '1 Week'),
        ('1M', '1 Month'),
    ], string='Timeframe', required=True, default='1d')
    
    # Additional Parameters
    period = fields.Integer(
        string='Period',
        help='Period used for calculation (e.g., 14 for RSI-14)'
    )
    custom_name = fields.Char(
        string='Custom Name',
        help='Custom name for custom indicators'
    )
    parameters = fields.Text(
        string='Parameters',
        help='JSON string of additional parameters used for calculation'
    )
    
    # Signal Information
    signal = fields.Selection([
        ('strong_buy', 'Strong Buy'),
        ('buy', 'Buy'),
        ('neutral', 'Neutral'),
        ('sell', 'Sell'),
        ('strong_sell', 'Strong Sell'),
    ], string='Signal', help='Trading signal based on indicator value')
    
    signal_strength = fields.Float(
        string='Signal Strength',
        digits=(5, 2),
        help='Signal strength from 0 to 100'
    )
    
    # Computed Fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    
    indicator_category = fields.Selection([
        ('trend', 'Trend'),
        ('momentum', 'Momentum'),
        ('volume', 'Volume'),
        ('volatility', 'Volatility'),
        ('support_resistance', 'Support/Resistance'),
    ], string='Category', compute='_compute_indicator_category', store=True)
    
    # Metadata
    created_date = fields.Datetime(string='Created Date', default=fields.Datetime.now)
    calculation_method = fields.Char(string='Calculation Method')
    data_source = fields.Char(string='Data Source')
    
    @api.depends('symbol_id', 'indicator', 'timestamp', 'timeframe')
    def _compute_display_name(self):
        for record in self:
            if record.symbol_id and record.indicator:
                indicator_name = record.custom_name or dict(record._fields['indicator'].selection).get(record.indicator, record.indicator)
                record.display_name = f"{record.symbol_id.symbol} - {indicator_name} ({record.timeframe})"
            else:
                record.display_name = 'Technical Indicator'
    
    @api.depends('indicator')
    def _compute_indicator_category(self):
        category_mapping = {
            # Trend indicators
            'sma_20': 'trend', 'sma_50': 'trend', 'sma_100': 'trend', 'sma_200': 'trend',
            'ema_12': 'trend', 'ema_26': 'trend', 'ema_50': 'trend', 'ema_200': 'trend',
            'macd_line': 'trend', 'macd_signal': 'trend', 'macd_histogram': 'trend',
            'adx': 'trend', 'aroon_up': 'trend', 'aroon_down': 'trend', 'parabolic_sar': 'trend',
            
            # Momentum indicators
            'rsi': 'momentum', 'rsi_14': 'momentum',
            'stoch_k': 'momentum', 'stoch_d': 'momentum',
            'williams_r': 'momentum', 'cci': 'momentum', 'mfi': 'momentum',
            
            # Volume indicators
            'volume_sma': 'volume', 'obv': 'volume', 'ad_line': 'volume',
            
            # Volatility indicators
            'bb_upper': 'volatility', 'bb_middle': 'volatility', 'bb_lower': 'volatility', 'bb_width': 'volatility',
            
            # Support/Resistance
            'pivot_point': 'support_resistance', 'resistance_1': 'support_resistance', 'resistance_2': 'support_resistance',
            'support_1': 'support_resistance', 'support_2': 'support_resistance',
        }
        
        for record in self:
            record.indicator_category = category_mapping.get(record.indicator, 'trend')
    
    @api.onchange('indicator')
    def _onchange_indicator(self):
        """Set default period based on indicator"""
        period_mapping = {
            'rsi': 14, 'rsi_14': 14,
            'sma_20': 20, 'sma_50': 50, 'sma_100': 100, 'sma_200': 200,
            'ema_12': 12, 'ema_26': 26, 'ema_50': 50, 'ema_200': 200,
            'stoch_k': 14, 'stoch_d': 3,
            'williams_r': 14, 'cci': 20, 'mfi': 14, 'adx': 14,
        }
        
        if self.indicator in period_mapping:
            self.period = period_mapping[self.indicator]
    
    @api.model
    def get_latest_indicators(self, symbol_id, timeframe='1d', indicators=None):
        """Get latest values for specified indicators"""
        domain = [
            ('symbol_id', '=', symbol_id),
            ('timeframe', '=', timeframe)
        ]
        
        if indicators:
            domain.append(('indicator', 'in', indicators))
        
        # Get the latest timestamp for each indicator
        latest_indicators = {}
        for indicator in indicators or []:
            latest = self.search(domain + [('indicator', '=', indicator)], 
                               order='timestamp desc', limit=1)
            if latest:
                latest_indicators[indicator] = {
                    'value': latest.value,
                    'signal': latest.signal,
                    'signal_strength': latest.signal_strength,
                    'timestamp': latest.timestamp,
                }
        
        return latest_indicators
    
    @api.model
    def calculate_rsi_signal(self, rsi_value):
        """Calculate RSI trading signal"""
        if rsi_value >= 80:
            return 'strong_sell', 90
        elif rsi_value >= 70:
            return 'sell', 70
        elif rsi_value <= 20:
            return 'strong_buy', 90
        elif rsi_value <= 30:
            return 'buy', 70
        else:
            return 'neutral', 50
    
    @api.model
    def calculate_macd_signal(self, macd_line, macd_signal):
        """Calculate MACD trading signal"""
        if macd_line > macd_signal:
            strength = min(abs(macd_line - macd_signal) * 100, 100)
            if strength > 50:
                return 'strong_buy', strength
            else:
                return 'buy', strength
        elif macd_line < macd_signal:
            strength = min(abs(macd_line - macd_signal) * 100, 100)
            if strength > 50:
                return 'strong_sell', strength
            else:
                return 'sell', strength
        else:
            return 'neutral', 50
    
    @api.model
    def bulk_create_indicators(self, indicator_data_list):
        """Bulk create technical indicator records"""
        created_records = []
        errors = []
        
        for data in indicator_data_list:
            try:
                # Check if record already exists
                existing = self.search([
                    ('symbol_id', '=', data.get('symbol_id')),
                    ('indicator', '=', data.get('indicator')),
                    ('timestamp', '=', data.get('timestamp')),
                    ('timeframe', '=', data.get('timeframe', '1d'))
                ], limit=1)
                
                if not existing:
                    # Calculate signal if not provided
                    if 'signal' not in data and data.get('indicator') == 'rsi':
                        signal, strength = self.calculate_rsi_signal(data.get('value', 0))
                        data['signal'] = signal
                        data['signal_strength'] = strength
                    
                    record = self.create(data)
                    created_records.append(record)
                else:
                    # Update existing record
                    existing.write({
                        'value': data.get('value'),
                        'signal': data.get('signal'),
                        'signal_strength': data.get('signal_strength'),
                        'parameters': data.get('parameters'),
                        'data_source': data.get('data_source'),
                    })
                    created_records.append(existing)
                    
            except Exception as e:
                errors.append({
                    'data': data,
                    'error': str(e)
                })
                _logger.error(f"Error creating technical indicator record: {e}")
        
        return {
            'created': created_records,
            'errors': errors,
            'success_count': len(created_records),
            'error_count': len(errors)
        }
    
    def get_indicator_summary(self):
        """Get a summary of the indicator for display"""
        self.ensure_one()
        
        summary = {
            'name': self.custom_name or dict(self._fields['indicator'].selection).get(self.indicator, self.indicator),
            'value': self.value,
            'signal': self.signal,
            'signal_strength': self.signal_strength,
            'category': self.indicator_category,
            'timestamp': self.timestamp,
        }
        
        # Add interpretation based on indicator type
        if self.indicator in ['rsi', 'rsi_14']:
            if self.value >= 70:
                summary['interpretation'] = 'Overbought'
            elif self.value <= 30:
                summary['interpretation'] = 'Oversold'
            else:
                summary['interpretation'] = 'Normal'
        
        return summary
