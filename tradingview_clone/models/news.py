# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging
import re
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class TradingViewNews(models.Model):
    _name = 'tradingview.news'
    _description = 'Financial News Articles'
    _order = 'published_at desc'
    _rec_name = 'title'
    _sql_constraints = [
        ('link_unique', 'UNIQUE(link)', 'News article link must be unique!'),
    ]

    # Core Fields
    symbol_id = fields.Many2one(
        'tradingview.symbol',
        string='Symbol',
        required=True,
        index=True,
        ondelete='cascade'
    )
    title = fields.Char(
        string='Title',
        required=True,
        help='News article title'
    )
    summary = fields.Text(
        string='Summary',
        help='Article summary or excerpt'
    )
    content = fields.Html(
        string='Content',
        help='Full article content (if available)'
    )
    link = fields.Char(
        string='Link',
        required=True,
        help='URL to the original article'
    )
    
    # Source Information
    source = fields.Char(
        string='Source',
        required=True,
        help='News source (e.g., Reuters, Bloomberg, Yahoo Finance)'
    )
    author = fields.Char(
        string='Author',
        help='Article author'
    )
    source_logo = fields.Char(
        string='Source Logo URL',
        help='URL to source logo image'
    )
    
    # Timestamps
    published_at = fields.Datetime(
        string='Published At',
        required=True,
        index=True,
        help='When the article was published'
    )
    created_date = fields.Datetime(
        string='Created Date',
        default=fields.Datetime.now
    )
    
    # Classification
    category = fields.Selection([
        ('earnings', 'Earnings'),
        ('merger', 'Merger & Acquisition'),
        ('regulatory', 'Regulatory'),
        ('market', 'Market Analysis'),
        ('company', 'Company News'),
        ('economic', 'Economic Data'),
        ('crypto', 'Cryptocurrency'),
        ('forex', 'Forex'),
        ('commodity', 'Commodity'),
        ('general', 'General'),
    ], string='Category', default='general')
    
    sentiment = fields.Selection([
        ('very_positive', 'Very Positive'),
        ('positive', 'Positive'),
        ('neutral', 'Neutral'),
        ('negative', 'Negative'),
        ('very_negative', 'Very Negative'),
    ], string='Sentiment', default='neutral')
    
    impact_level = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ], string='Impact Level', default='medium')
    
    # Engagement Metrics
    views_count = fields.Integer(string='Views', default=0)
    likes_count = fields.Integer(string='Likes', default=0)
    shares_count = fields.Integer(string='Shares', default=0)
    comments_count = fields.Integer(string='Comments', default=0)
    
    # Content Analysis
    keywords = fields.Char(
        string='Keywords',
        help='Comma-separated keywords extracted from the article'
    )
    language = fields.Char(
        string='Language',
        default='en',
        help='Article language code'
    )
    word_count = fields.Integer(
        string='Word Count',
        compute='_compute_word_count',
        store=True
    )
    
    # Status and Flags
    active = fields.Boolean(string='Active', default=True)
    is_featured = fields.Boolean(string='Featured', default=False)
    is_breaking = fields.Boolean(string='Breaking News', default=False)
    is_verified = fields.Boolean(string='Verified', default=True)
    
    # Computed Fields
    age_hours = fields.Float(
        string='Age (Hours)',
        compute='_compute_age',
        help='Hours since publication'
    )
    is_recent = fields.Boolean(
        string='Recent',
        compute='_compute_age',
        help='Published within last 24 hours'
    )
    display_summary = fields.Text(
        string='Display Summary',
        compute='_compute_display_summary',
        help='Truncated summary for display'
    )
    
    @api.depends('summary', 'content')
    def _compute_word_count(self):
        for record in self:
            text = record.content or record.summary or ''
            # Remove HTML tags and count words
            clean_text = re.sub(r'<[^>]+>', '', text)
            words = len(clean_text.split()) if clean_text else 0
            record.word_count = words
    
    @api.depends('published_at')
    def _compute_age(self):
        now = fields.Datetime.now()
        for record in self:
            if record.published_at:
                delta = now - record.published_at
                record.age_hours = delta.total_seconds() / 3600
                record.is_recent = delta.total_seconds() < 86400  # 24 hours
            else:
                record.age_hours = 0
                record.is_recent = False
    
    @api.depends('summary')
    def _compute_display_summary(self):
        for record in self:
            if record.summary:
                # Truncate to 200 characters
                if len(record.summary) > 200:
                    record.display_summary = record.summary[:197] + '...'
                else:
                    record.display_summary = record.summary
            else:
                record.display_summary = ''
    
    @api.constrains('published_at')
    def _check_published_date(self):
        for record in self:
            if record.published_at and record.published_at > fields.Datetime.now():
                raise ValidationError(_('Published date cannot be in the future.'))
    
    @api.constrains('link')
    def _check_link_format(self):
        for record in self:
            if record.link and not record.link.startswith(('http://', 'https://')):
                raise ValidationError(_('Link must be a valid URL starting with http:// or https://'))
    
    @api.model
    def get_latest_news(self, symbol_id=None, limit=10, category=None):
        """Get latest news articles"""
        domain = [('active', '=', True)]
        
        if symbol_id:
            domain.append(('symbol_id', '=', symbol_id))
        
        if category:
            domain.append(('category', '=', category))
        
        return self.search(domain, order='published_at desc', limit=limit)
    
    @api.model
    def get_breaking_news(self, limit=5):
        """Get breaking news articles"""
        return self.search([
            ('is_breaking', '=', True),
            ('active', '=', True)
        ], order='published_at desc', limit=limit)
    
    @api.model
    def get_featured_news(self, limit=5):
        """Get featured news articles"""
        return self.search([
            ('is_featured', '=', True),
            ('active', '=', True)
        ], order='published_at desc', limit=limit)
    
    @api.model
    def search_news(self, query, symbol_id=None, limit=20):
        """Search news articles by query"""
        domain = [
            ('active', '=', True),
            '|', '|',
            ('title', 'ilike', query),
            ('summary', 'ilike', query),
            ('keywords', 'ilike', query)
        ]
        
        if symbol_id:
            domain.append(('symbol_id', '=', symbol_id))
        
        return self.search(domain, order='published_at desc', limit=limit)
    
    @api.model
    def bulk_create_news(self, news_data_list):
        """Bulk create news articles with duplicate checking"""
        created_records = []
        errors = []
        duplicates = []
        
        for data in news_data_list:
            try:
                # Check for duplicates by link
                existing = self.search([('link', '=', data.get('link'))], limit=1)
                
                if existing:
                    duplicates.append(data)
                    continue
                
                # Auto-detect category based on title/summary
                if 'category' not in data:
                    data['category'] = self._detect_category(data.get('title', ''), data.get('summary', ''))
                
                # Auto-detect sentiment (basic implementation)
                if 'sentiment' not in data:
                    data['sentiment'] = self._detect_sentiment(data.get('title', ''), data.get('summary', ''))
                
                record = self.create(data)
                created_records.append(record)
                
            except Exception as e:
                errors.append({
                    'data': data,
                    'error': str(e)
                })
                _logger.error(f"Error creating news record: {e}")
        
        return {
            'created': created_records,
            'errors': errors,
            'duplicates': duplicates,
            'success_count': len(created_records),
            'error_count': len(errors),
            'duplicate_count': len(duplicates)
        }
    
    @api.model
    def _detect_category(self, title, summary):
        """Simple category detection based on keywords"""
        text = (title + ' ' + (summary or '')).lower()
        
        if any(word in text for word in ['earnings', 'quarterly', 'revenue', 'profit']):
            return 'earnings'
        elif any(word in text for word in ['merger', 'acquisition', 'buyout', 'takeover']):
            return 'merger'
        elif any(word in text for word in ['sec', 'fda', 'regulatory', 'compliance']):
            return 'regulatory'
        elif any(word in text for word in ['bitcoin', 'ethereum', 'crypto', 'blockchain']):
            return 'crypto'
        elif any(word in text for word in ['forex', 'currency', 'exchange rate']):
            return 'forex'
        elif any(word in text for word in ['oil', 'gold', 'commodity', 'crude']):
            return 'commodity'
        elif any(word in text for word in ['gdp', 'inflation', 'fed', 'interest rate']):
            return 'economic'
        else:
            return 'general'
    
    @api.model
    def _detect_sentiment(self, title, summary):
        """Simple sentiment detection based on keywords"""
        text = (title + ' ' + (summary or '')).lower()
        
        positive_words = ['up', 'rise', 'gain', 'profit', 'growth', 'success', 'positive', 'bullish']
        negative_words = ['down', 'fall', 'loss', 'decline', 'negative', 'bearish', 'crash']
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        if positive_count > negative_count:
            return 'positive' if positive_count - negative_count == 1 else 'very_positive'
        elif negative_count > positive_count:
            return 'negative' if negative_count - positive_count == 1 else 'very_negative'
        else:
            return 'neutral'
    
    def increment_views(self):
        """Increment view count"""
        self.views_count += 1
    
    def toggle_featured(self):
        """Toggle featured status"""
        self.is_featured = not self.is_featured
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Featured Status Updated'),
                'message': _('Article %s featured status') % ('added to' if self.is_featured else 'removed from'),
                'type': 'success',
            }
        }
    
    def mark_as_breaking(self):
        """Mark as breaking news"""
        self.is_breaking = True
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Breaking News'),
                'message': _('Article marked as breaking news'),
                'type': 'success',
            }
        }
