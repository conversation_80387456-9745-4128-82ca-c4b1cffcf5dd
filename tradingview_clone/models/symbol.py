# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import re
import logging
from .ohlc import ASSET_TYPE_SELECTION

_logger = logging.getLogger(__name__)


class TradingViewSymbol(models.Model):
    _name = 'tradingview.symbol'
    _description = 'Financial Symbol'
    _order = 'symbol asc'
    _rec_name = 'symbol'
    _sql_constraints = [
        ('symbol_unique', 'UNIQUE(symbol)', 'Symbol must be unique!'),
        ('slug_unique', 'UNIQUE(slug)', 'Slug must be unique!'),
    ]

    # Basic Information
    name = fields.Char(
        string='Full Name',
        required=True,
        help='Full company or asset name (e.g., Apple Inc., Bitcoin USD)'
    )
    symbol = fields.Char(
        string='Symbol',
        required=True,
        index=True,
        help='Trading symbol (e.g., AAPL, BTCUSD, EURUSD)'
    )
    slug = fields.Char(
        string='URL Slug',
        required=True,
        index=True,
        help='URL-safe slug for routing (e.g., aapl, btcusd)'
    )
    
    # Market Information
    exchange = fields.Char(
        string='Exchange',
        help='Exchange where the symbol is traded (e.g., NASDAQ, NYSE, Binance)'
    )
    region = fields.Char(
        string='Region',
        help='Country or market region (e.g., US, EU, Global)'
    )
    currency = fields.Char(
        string='Currency',
        help='Trading currency (e.g., USD, EUR, BTC)'
    )
    
    # Classification
    sector = fields.Char(
        string='Sector',
        help='Sector classification (e.g., Technology, Healthcare)'
    )
    industry = fields.Char(
        string='Industry',
        help='Industry classification (e.g., Software, Pharmaceuticals)'
    )
    isin = fields.Char(
        string='ISIN',
        help='International Securities Identification Number'
    )
    type = fields.Selection(
        ASSET_TYPE_SELECTION,
        string='Type',
        required=True,
        default='stock'
    )
    
    # Status and Metadata
    active = fields.Boolean(string='Active', default=True)
    description = fields.Text(string='Description')
    website = fields.Char(string='Website')
    logo_url = fields.Char(string='Logo URL')
    
    # Market Data (Latest)
    current_price = fields.Float(string='Current Price', digits=(16, 6))
    daily_change = fields.Float(string='Daily Change', digits=(16, 6))
    daily_change_percent = fields.Float(string='Daily Change %', digits=(16, 4))
    volume = fields.Float(string='Volume')
    market_cap = fields.Float(string='Market Cap')
    
    # Price Ranges
    week_52_high = fields.Float(string='52 Week High', digits=(16, 6))
    week_52_low = fields.Float(string='52 Week Low', digits=(16, 6))
    day_high = fields.Float(string='Day High', digits=(16, 6))
    day_low = fields.Float(string='Day Low', digits=(16, 6))
    
    # Timestamps
    last_updated = fields.Datetime(string='Last Updated')
    created_date = fields.Datetime(string='Created Date', default=fields.Datetime.now)
    
    # Relationships
    ohlc_ids = fields.One2many('tradingview.ohlc', 'symbol_id', string='OHLC Data')
    technical_ids = fields.One2many('tradingview.technical', 'symbol_id', string='Technical Indicators')
    news_ids = fields.One2many('tradingview.news', 'symbol_id', string='News Articles')
    event_ids = fields.One2many('tradingview.event', 'symbol_id', string='Events')
    watchlist_ids = fields.One2many('tradingview.watchlist', 'symbol_id', string='Watchlists')
    
    # Computed Fields
    ohlc_count = fields.Integer(string='OHLC Records', compute='_compute_counts')
    news_count = fields.Integer(string='News Count', compute='_compute_counts')
    watchlist_count = fields.Integer(string='Watchlist Count', compute='_compute_counts')
    
    @api.depends('ohlc_ids', 'news_ids', 'watchlist_ids')
    def _compute_counts(self):
        for record in self:
            record.ohlc_count = len(record.ohlc_ids)
            record.news_count = len(record.news_ids)
            record.watchlist_count = len(record.watchlist_ids)
    
    @api.model
    def create(self, vals):
        if 'slug' not in vals and 'symbol' in vals:
            vals['slug'] = self._generate_slug(vals['symbol'])
        return super().create(vals)
    
    def write(self, vals):
        if 'symbol' in vals and 'slug' not in vals:
            vals['slug'] = self._generate_slug(vals['symbol'])
        return super().write(vals)
    
    def _generate_slug(self, symbol):
        """Generate URL-safe slug from symbol"""
        if not symbol:
            return ''
        # Convert to lowercase and replace special characters
        slug = re.sub(r'[^a-zA-Z0-9]', '', symbol.lower())
        return slug
    
    @api.constrains('symbol')
    def _check_symbol_format(self):
        for record in self:
            if record.symbol and not re.match(r'^[A-Z0-9]+$', record.symbol):
                raise ValidationError(_('Symbol must contain only uppercase letters and numbers.'))
    
    @api.constrains('slug')
    def _check_slug_format(self):
        for record in self:
            if record.slug and not re.match(r'^[a-z0-9]+$', record.slug):
                raise ValidationError(_('Slug must contain only lowercase letters and numbers.'))
    
    def name_get(self):
        result = []
        for record in self:
            name = f"{record.symbol} - {record.name}"
            if record.exchange:
                name += f" ({record.exchange})"
            result.append((record.id, name))
        return result
    
    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        args = args or []
        if name:
            # Search by symbol or name
            domain = ['|', ('symbol', operator, name), ('name', operator, name)]
            records = self.search(domain + args, limit=limit)
            return records.name_get()
        return super().name_search(name, args, operator, limit)
    
    def get_latest_ohlc(self, limit=1):
        """Get latest OHLC data for this symbol"""
        return self.ohlc_ids.search([
            ('symbol_id', '=', self.id)
        ], order='timestamp desc', limit=limit)
    
    def get_latest_news(self, limit=5):
        """Get latest news for this symbol"""
        return self.news_ids.search([
            ('symbol_id', '=', self.id)
        ], order='published_at desc', limit=limit)
    
    def get_upcoming_events(self, limit=5):
        """Get upcoming events for this symbol"""
        return self.event_ids.search([
            ('symbol_id', '=', self.id),
            ('date', '>=', fields.Datetime.now())
        ], order='date asc', limit=limit)
    
    def update_current_price(self, price_data):
        """Update current price and related fields"""
        self.write({
            'current_price': price_data.get('price', 0),
            'daily_change': price_data.get('change', 0),
            'daily_change_percent': price_data.get('change_percent', 0),
            'volume': price_data.get('volume', 0),
            'day_high': price_data.get('high', 0),
            'day_low': price_data.get('low', 0),
            'last_updated': fields.Datetime.now(),
        })
    
    def action_sync_data(self):
        """Manual action to sync symbol data"""
        # This will be implemented in the API integration
        self.ensure_one()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Sync Started'),
                'message': _('Data synchronization started for %s') % self.symbol,
                'type': 'success',
            }
        }
