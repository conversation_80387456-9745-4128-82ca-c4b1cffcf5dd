# -*- coding: utf-8 -*-

"""
Common selection field definitions to avoid duplicates across models.
This prevents database conflicts during module installation.
"""

# Timeframe selection used in OHLC and Technical models
TIMEFRAME_SELECTION = [
    ('1m', '1 Minute'),
    ('5m', '5 Minutes'),
    ('15m', '15 Minutes'),
    ('30m', '30 Minutes'),
    ('1h', '1 Hour'),
    ('4h', '4 Hours'),
    ('1d', '1 Day'),
    ('1w', '1 Week'),
    ('1M', '1 Month'),
]

# Impact level selection used in News and Event models
IMPACT_LEVEL_SELECTION = [
    ('low', 'Low'),
    ('medium', 'Medium'),
    ('high', 'High'),
    ('critical', 'Critical'),
]

# Status selection for various models
STATUS_SELECTION = [
    ('active', 'Active'),
    ('inactive', 'Inactive'),
    ('pending', 'Pending'),
    ('cancelled', 'Cancelled'),
    ('completed', 'Completed'),
]

# Signal selection for technical indicators
SIGNAL_SELECTION = [
    ('strong_buy', 'Strong Buy'),
    ('buy', 'Buy'),
    ('neutral', 'Neutral'),
    ('sell', 'Sell'),
    ('strong_sell', 'Strong Sell'),
]

# Sentiment selection for news
SENTIMENT_SELECTION = [
    ('very_positive', 'Very Positive'),
    ('positive', 'Positive'),
    ('neutral', 'Neutral'),
    ('negative', 'Negative'),
    ('very_negative', 'Very Negative'),
]
